import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
import time
from datetime import datetime

class ChatSummaryUI06151:
    def __init__(self, parent, callbacks):
        """
        初始化聊天总结UI
        :param parent: 父窗口
        :param callbacks: 回调函数字典
        """
        self.parent = parent
        self.callbacks = callbacks
        
        # 聊天总结设置变量
        self.accumulated_message_count06151 = tk.StringVar(value="100")  # 默认累计消息数量
        self.chat_summary_prompt06151 = tk.StringVar(value="")  # 聊天总结提示词
        
        # 联系人聊天总结数据
        self.contacts_summary_data06151 = {}  # 存储联系人总结数据
        self.contacts_unsummarized_count06151 = {}  # 存储联系人未总结消息数量
        
        # 创建主框架
        self.main_frame06151 = ttk.Frame(parent)
        self.main_frame06151.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建UI元素
        self.create_ui_elements()
        
        # 加载聊天总结数据
        self.load_summary_data()
    
    def create_ui_elements(self):
        """创建UI元素"""
        # 添加标题
        ttk.Label(self.main_frame06151, text="聊天总结设置", font=('微软雅黑', 11, 'bold')).pack(anchor=tk.W, padx=5, pady=10)
        
        # 设置累计消息数量
        count_frame06151 = ttk.Frame(self.main_frame06151)
        count_frame06151.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(count_frame06151, text="累计消息数量:").pack(side=tk.LEFT, padx=5)
        count_entry06151 = ttk.Entry(count_frame06151, textvariable=self.accumulated_message_count06151, width=10)
        count_entry06151.pack(side=tk.LEFT, padx=5)
        ttk.Label(count_frame06151, text="(建议：100)").pack(side=tk.LEFT, padx=5)
        
        # 添加刷新按钮
        refresh_btn06151 = ttk.Button(
            count_frame06151,
            text="刷新列表",
            command=self.load_summary_data
        )
        refresh_btn06151.pack(side=tk.RIGHT, padx=5)
        
        # 中央区域表单：可总结联系人列表
        contact_frame06151 = ttk.LabelFrame(self.main_frame06151, text="可总结联系人列表")
        contact_frame06151.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)
        
        # 创建一个包含树形列表和按钮的框架
        list_container06151 = ttk.Frame(contact_frame06151)
        list_container06151.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview显示联系人列表
        self.contacts_tree06151 = ttk.Treeview(list_container06151, columns=("联系人", "未总结消息数"), show="headings", height=8)
        self.contacts_tree06151.heading("联系人", text="联系人")
        self.contacts_tree06151.heading("未总结消息数", text="未总结消息数")
        
        # 调整列宽
        self.contacts_tree06151.column("联系人", width=200)
        self.contacts_tree06151.column("未总结消息数", width=100)
        
        self.contacts_tree06151.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar06151 = ttk.Scrollbar(list_container06151, orient="vertical", command=self.contacts_tree06151.yview)
        scrollbar06151.pack(side=tk.RIGHT, fill=tk.Y)
        self.contacts_tree06151.configure(yscrollcommand=scrollbar06151.set)
        
        # 添加按钮框架
        buttons_frame06151 = ttk.Frame(contact_frame06151)
        buttons_frame06151.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加按钮
        view_btn06151 = ttk.Button(
            buttons_frame06151,
            text="查看历史总结",
            command=self.view_selected_summary
        )
        view_btn06151.pack(side=tk.LEFT, padx=5)
        
        summary_btn06151 = ttk.Button(
            buttons_frame06151,
            text="立即总结",
            command=self.summarize_selected
        )
        summary_btn06151.pack(side=tk.LEFT, padx=5)
        
        # 添加全部总结按钮
        summary_all_btn06151 = ttk.Button(
            buttons_frame06151,
            text="全部总结",
            command=self.show_all_contacts
        )
        summary_all_btn06151.pack(side=tk.LEFT, padx=5)
        
        # 聊天总结提示词设置
        prompt_frame06151 = ttk.LabelFrame(self.main_frame06151, text="聊天总结提示词")
        prompt_frame06151.pack(fill=tk.X, padx=5, pady=10)
        
        prompt_text06151 = tk.Text(prompt_frame06151, height=8, width=60)
        prompt_text06151.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 获取当前提示词
        current_prompt06151 = self.chat_summary_prompt06151.get()
        
        # 如果没有提示词，设置默认值
        if not current_prompt06151:
            default_prompt06151 = "你是一个聊天总结专家，有着丰富的识别问题和提炼要点的能力。\n以下是上次总结的结果{last_summary}\n请将上一次的总结内容和以下与特定联系人的聊天对话新增内容进行合并总结：\n{chat_content}\n\n总结输出格式：\n1.联系人的主要话题和关注点\n2.联系人的语气和情绪\n3.对话中的重要信息和决定\n4.整体总结。"
            current_prompt06151 = default_prompt06151
            self.chat_summary_prompt06151.set(default_prompt06151)
        
        # 设置文本框内容
        prompt_text06151.insert("1.0", current_prompt06151)
            
        # 绑定文本变化事件
        def on_prompt_change(event=None):
            self.chat_summary_prompt06151.set(prompt_text06151.get("1.0", "end-1c"))
        prompt_text06151.bind("<KeyRelease>", on_prompt_change)
        
        # 底部保存按钮
        button_frame06151 = ttk.Frame(self.main_frame06151)
        button_frame06151.pack(fill=tk.X, padx=5, pady=15)
        
        save_btn06151 = ttk.Button(
            button_frame06151, 
            text="保存设置", 
            command=self.save_chat_summary_settings
        )
        save_btn06151.pack(side=tk.RIGHT, padx=5)
    
    def load_summary_data(self):
        """加载聊天总结数据和未总结消息"""
        try:
            # 确保summary_chat文件夹存在
            if not os.path.exists("summary_chat"):
                os.makedirs("summary_chat")
            
            # 清空现有数据
            self.contacts_summary_data06151 = {}
            self.contacts_unsummarized_count06151 = {}
            
            # 加载已有的总结数据
            for filename in os.listdir("summary_chat"):
                if filename.endswith(".json"):
                    contact_name = os.path.splitext(filename)[0]
                    file_path = os.path.join("summary_chat", filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            summary_data = json.load(f)
                            self.contacts_summary_data06151[contact_name] = summary_data
                    except Exception as e:
                        print(f"读取总结文件 {file_path} 出错: {str(e)}")
            
            # 加载聊天记录
            from utils.storage import StorageManager
            storage = StorageManager()
            conversations, _ = storage.load_all_conversations()
            
            # 计算未总结消息数量
            for contact_name, conversation in conversations.items():
                last_summary_time = 0
                
                # 检查是否有总结记录
                if contact_name in self.contacts_summary_data06151 and self.contacts_summary_data06151[contact_name]:
                    last_summary = self.contacts_summary_data06151[contact_name][-1]
                    last_summary_time = last_summary.get("last_time_summary", 0)
                
                # 统计大于last_summary_time的消息数量
                unsummarized_count = 0
                for msg in conversation:
                    if msg.get("timestamp", 0) > last_summary_time:
                        unsummarized_count += 1
                
                # 保存未总结消息数量
                self.contacts_unsummarized_count06151[contact_name] = unsummarized_count
            
            # 更新UI
            self.update_contact_list()
            
        except Exception as e:
            print(f"加载聊天总结数据时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def view_selected_summary(self):
        """查看选中联系人的历史总结"""
        selected_items = self.contacts_tree06151.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择一个联系人")
            return
        
        # 获取选中项的联系人名称
        item = selected_items[0]
        contact_name = self.contacts_tree06151.item(item, "values")[0]
        
        # 显示历史总结
        self.show_history_summary(contact_name)
    
    def summarize_selected(self):
        """立即总结选中联系人的聊天记录"""
        selected_items = self.contacts_tree06151.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择一个联系人")
            return
        
        # 获取选中项的联系人名称
        item = selected_items[0]
        contact_name = self.contacts_tree06151.item(item, "values")[0]
        
        # 生成总结
        self.generate_summary_now(contact_name)
    
    def update_contact_list(self):
        """更新联系人列表"""
        # 清空现有列表
        for item in self.contacts_tree06151.get_children():
            self.contacts_tree06151.delete(item)
        
        # 获取设定的累计消息数量
        try:
            accumulated_count = int(self.accumulated_message_count06151.get())
        except ValueError:
            accumulated_count = 100  # 默认值
        
        # 创建已处理联系人集合，避免重复显示
        processed_contacts = set()
        
        # 首先添加未总结消息数大于等于设定值的联系人, 并按未总结消息数排序  
        for contact_name, unsummarized_count in sorted(self.contacts_unsummarized_count06151.items(), 
                                                      key=lambda x: x[1], reverse=True):
            # 只显示未总结消息数大于等于设定值的联系人
            if unsummarized_count >= accumulated_count:
                self.contacts_tree06151.insert("", tk.END, values=(
                    contact_name, 
                    unsummarized_count
                ))
                processed_contacts.add(contact_name)
        
        # 不再添加未总结消息数小于设定值的联系人
        # 原来的代码被删除
    
    def show_history_summary(self, contact_name):
        """显示历史总结"""
        if contact_name in self.contacts_summary_data06151 and self.contacts_summary_data06151[contact_name]:
            last_summary = self.contacts_summary_data06151[contact_name][-1]
            
            # 创建弹窗
            summary_window = tk.Toplevel(self.parent)
            summary_window.title(f"{contact_name} - 历史聊天总结")
            summary_window.geometry("500x400")
            
            # 格式化时间
            timestamp = last_summary.get("last_time_summary", 0)
            time_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp else "未知"
            
            # 显示联系人和时间
            ttk.Label(summary_window, text=f"联系人: {contact_name}", font=('微软雅黑', 12, 'bold')).pack(anchor=tk.W, padx=10, pady=5)
            ttk.Label(summary_window, text=f"总结时间: {time_str}").pack(anchor=tk.W, padx=10, pady=5)
            
            # 显示总结内容
            summary_frame = ttk.Frame(summary_window)
            summary_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
            
            summary_text = tk.Text(summary_frame, wrap=tk.WORD)
            summary_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(summary_frame, orient="vertical", command=summary_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            summary_text.configure(yscrollcommand=scrollbar.set)
            
            # 插入总结内容
            summary_text.insert("1.0", last_summary.get("summary_content", "无总结内容"))
            summary_text.config(state=tk.DISABLED)  # 设置为只读
            
            # 添加关闭按钮
            ttk.Button(summary_window, text="关闭", command=summary_window.destroy).pack(pady=10)
        else:
            messagebox.showinfo("提示", f"{contact_name} 没有历史总结记录")
    
    def generate_summary_now(self, contact_name):
        """立即为指定联系人生成总结"""
        if self.callbacks.get('generate_chat_summary'):
            # 定义总结完成后的回调
            def on_summary_generated():
                # 重新加载总结数据
                self.load_summary_data()
                
            # 调用总结函数，并传递回调
            self.callbacks['generate_chat_summary'](contact_name)
            
            # 设置一个定时器，3秒后重新加载数据
            # 这是为了确保总结完成后数据已经写入文件
            self.parent.after(3000, self.load_summary_data)
        else:
            messagebox.showwarning("警告", "总结功能未实现")
    
    def save_chat_summary_settings(self):
        """保存聊天总结设置"""
        settings = {
            'accumulated_message_count06151': self.accumulated_message_count06151.get(),
            'chat_summary_prompt06151': self.chat_summary_prompt06151.get(),
        }
        
        if self.callbacks.get('save_chat_summary_settings'):
            self.callbacks['save_chat_summary_settings'](settings)
            # 更新联系人列表
            self.load_summary_data()
            messagebox.showinfo("保存成功", "聊天总结设置已保存")
        else:
            messagebox.showwarning("警告", "保存设置回调函数未实现")
    
    def set_chat_summary_settings(self, settings):
        """设置聊天总结设置"""
        self.accumulated_message_count06151.set(settings.get('accumulated_message_count06151', '100'))
        self.chat_summary_prompt06151.set(settings.get('chat_summary_prompt06151', ''))
        
        # 更新UI
        self.load_summary_data()
    
    def show_all_contacts(self):
        """显示所有联系人并执行批量总结操作"""
        # 清空现有列表
        for item in self.contacts_tree06151.get_children():
            self.contacts_tree06151.delete(item)
        
        # 创建已处理联系人集合，避免重复显示
        processed_contacts = set()
        
        # 首先添加有未总结消息的联系人
        for contact_name, unsummarized_count in sorted(self.contacts_unsummarized_count06151.items(), 
                                                      key=lambda x: x[1], reverse=True):
            self.contacts_tree06151.insert("", tk.END, values=(
                contact_name, 
                unsummarized_count
            ))
            processed_contacts.add(contact_name)
        
        # 然后添加已有历史总结但没有未总结消息的联系人
        for contact_name in self.contacts_summary_data06151:
            if contact_name not in processed_contacts:
                unsummarized_count = 0
                self.contacts_tree06151.insert("", tk.END, values=(
                    contact_name, 
                    unsummarized_count
                ))
                processed_contacts.add(contact_name)
        
        # 添加所有其他联系人
        from utils.storage import StorageManager
        storage = StorageManager()
        conversations, _ = storage.load_all_conversations()
        
        for contact_name in conversations:
            if contact_name not in processed_contacts:
                unsummarized_count = self.contacts_unsummarized_count06151.get(contact_name, 0)
                self.contacts_tree06151.insert("", tk.END, values=(
                    contact_name, 
                    unsummarized_count
                ))
                processed_contacts.add(contact_name)
        
        # 获取需要总结的联系人列表（未总结消息数大于0的联系人）
        contacts_to_summarize = []
        for contact_name, unsummarized_count in self.contacts_unsummarized_count06151.items():
            if unsummarized_count > 0:
                contacts_to_summarize.append((contact_name, unsummarized_count))
        
        # 按未总结消息数排序
        contacts_to_summarize.sort(key=lambda x: x[1], reverse=True)
        
        # 如果有需要总结的联系人，弹出确认对话框
        if contacts_to_summarize:
            confirm = messagebox.askyesno("批量总结确认", 
                                         f"确定要对{len(contacts_to_summarize)}个联系人进行批量总结吗？\n"
                                         f"这可能需要一些时间。")
            if confirm:
                self.batch_summarize_contacts(contacts_to_summarize)
        else:
            messagebox.showinfo("提示", "没有需要总结的联系人")
    
    def batch_summarize_contacts(self, contacts_to_summarize):
        """批量总结多个联系人的聊天记录"""
        if not self.callbacks.get('generate_chat_summary'):
            messagebox.showwarning("警告", "总结功能未实现")
            return
        
        # 创建进度窗口
        progress_window = tk.Toplevel(self.parent)
        progress_window.title("批量聊天总结进度")
        progress_window.geometry("500x400")
        progress_window.resizable(False, False)
        
        # 窗口居中
        window_width = 500
        window_height = 400
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 添加标题
        title_label = ttk.Label(progress_window, text="正在执行批量聊天总结...", font=('微软雅黑', 12, 'bold'))
        title_label.pack(pady=10)
        
        # 添加总进度条
        progress_frame = ttk.Frame(progress_window)
        progress_frame.pack(fill=tk.X, padx=20, pady=5)
        
        ttk.Label(progress_frame, text="总进度:").pack(side=tk.LEFT, padx=5)
        total_progress = ttk.Progressbar(progress_frame, orient="horizontal", length=350, mode="determinate")
        total_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        progress_label = ttk.Label(progress_frame, text="0%")
        progress_label.pack(side=tk.LEFT, padx=5)
        
        # 添加当前处理状态
        status_frame = ttk.Frame(progress_window)
        status_frame.pack(fill=tk.X, padx=20, pady=5)
        
        ttk.Label(status_frame, text="当前处理:").pack(side=tk.LEFT, padx=5)
        current_contact_label = ttk.Label(status_frame, text="准备中...", width=30)
        current_contact_label.pack(side=tk.LEFT, padx=5)
        
        # 添加日志列表框
        log_frame = ttk.LabelFrame(progress_window, text="处理日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        log_text.configure(yscrollcommand=scrollbar.set)
        
        # 添加取消按钮
        button_frame = ttk.Frame(progress_window)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        cancel_button = ttk.Button(button_frame, text="取消", command=progress_window.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # 更新日志的函数
        def update_log(message):
            log_text.insert(tk.END, f"{message}\n")
            log_text.see(tk.END)  # 滚动到底部
            log_text.update()
        
        # 总结完成的计数器
        completed_count = [0]
        total_count = len(contacts_to_summarize)
        
        # 更新进度的函数
        def update_progress():
            percentage = int(completed_count[0] / total_count * 100)
            total_progress["value"] = percentage
            progress_label.config(text=f"{percentage}%")
            progress_window.update()
        
        # 添加初始日志
        update_log(f"开始批量总结 {total_count} 个联系人的聊天记录")
        
        # 定义总结完成后的回调
        def on_summary_complete(contact_name):
            completed_count[0] += 1
            update_log(f"✓ 完成 {contact_name} 的聊天总结 ({completed_count[0]}/{total_count})")
            update_progress()
            
            # 如果全部完成
            if completed_count[0] >= total_count:
                update_log("所有联系人的聊天总结已完成!")
                cancel_button.config(text="关闭")
                # 重新加载总结数据
                self.parent.after(2000, self.load_summary_data)
            else:
                # 继续处理下一个联系人
                process_next_contact()
        
        # 处理下一个联系人的函数
        def process_next_contact():
            if completed_count[0] < total_count:
                next_contact, msg_count = contacts_to_summarize[completed_count[0]]
                current_contact_label.config(text=f"{next_contact} ({msg_count}条消息)")
                update_log(f"正在总结 {next_contact} 的聊天记录...")
                
                # 调用总结函数
                try:
                    self.callbacks['generate_chat_summary'](next_contact)
                    # 设置一个定时器，模拟总结完成
                    self.parent.after(3000, lambda: on_summary_complete(next_contact))
                except Exception as e:
                    update_log(f"总结 {next_contact} 时出错: {str(e)}")
                    on_summary_complete(next_contact)  # 继续下一个
        
        # 开始处理第一个联系人
        self.parent.after(500, process_next_contact) 
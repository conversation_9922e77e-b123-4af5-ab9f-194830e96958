import tkinter as tk
from tkinter import ttk, scrolledtext
from PIL import Image, ImageTk
import cv2
import numpy as np
import time

class MainUI:
    def __init__(self, root, callbacks):
        """
        初始化主界面
        :param root: Tkinter根窗口
        :param callbacks: 回调函数字典，包含各种事件处理函数
        """
        self.root = root
        self.callbacks = callbacks
        
        # 初始化UI元素
        self.preview_label = None
        self.log_text = None
        self.log_queue = None
        self.paned_window = None
        
        # 设置标题和窗口尺寸
        self.root.title("茧辉职梦AI客服")
        self.root.geometry("820x1050")
        self.root.resizable(True, True)
        self.root.configure(bg="#f0f2f5")  # 更柔和的背景色
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建并配置样式
        self.setup_style()
        
        # 使用Notebook创建选项卡式界面
        self.tab_control = ttk.Notebook(self.root)
        self.tab_control.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 主界面选项卡
        main_tab = ttk.Frame(self.tab_control)
        settings_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(main_tab, text="主界面")
        self.tab_control.add(settings_tab, text="设置")
        
        # 创建主界面内容
        self.create_control_panel(main_tab)
        self.create_preview_and_log_area(main_tab)
        
        # 创建设置界面 - 直接调用回调函数创建
        print("准备调用create_settings_tab回调函数")
        if 'create_settings_tab' in self.callbacks:
            print(f"回调函数存在，开始调用")
            result = self.callbacks['create_settings_tab'](settings_tab)
            print(f"回调函数调用完成，返回结果类型: {type(result)}")
        else:
            print("警告：create_settings_tab回调函数未定义")
            
        # 创建帮助选项卡
        self.create_help_tab()
        
        # 初始设置日志区滚动到顶部
        def show_log_area():
            # 设置PanedWindow的位置，确保日志区域可见
            # 这会在初始化界面后执行
            total_height = self.paned_window.winfo_height()
            if total_height > 10:  # 确保窗口已经渲染
                # 设置分隔位置约为总高度的60%，确保日志区可见
                split_pos = int(total_height * 0.6)
                self.paned_window.sashpos(0, split_pos)
            else:
                # 如果窗口尚未完全渲染，延迟执行
                self.root.after(100, show_log_area)
        
        # 在UI完全加载后执行
        self.root.after(300, show_log_area)
    
    def setup_style(self):
        """设置UI样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 设置各种样式
        style.configure('TButton', font=('微软雅黑', 11), padding=6, background='#4a7abc', foreground='#ffffff')
        style.map('TButton', 
                 background=[('active', '#3a5d9c'), ('pressed', '#2c4a7c'), ('disabled', '#cccccc')], 
                 foreground=[('active', '#ffffff'), ('disabled', '#999999')],
                 relief=[('pressed', 'sunken'), ('!pressed', 'raised')])
        
        # 创建自定义样式的按钮
        style.configure('Primary.TButton', 
                        font=('微软雅黑', 11, 'bold'), 
                        background='#4a7abc', 
                        foreground='#ffffff', 
                        padding=(15, 8))
        style.map('Primary.TButton', 
                 background=[('active', '#3a5d9c'), ('pressed', '#2c4a7c'), ('disabled', '#cccccc')], 
                 foreground=[('active', '#ffffff'), ('disabled', '#999999')],
                 relief=[('pressed', 'sunken'), ('!pressed', 'raised')])
        
        # 创建较小的按钮样式
        style.configure('Small.TButton', 
                        font=('微软雅黑', 10), 
                        background='#5a6a7a', 
                        foreground='#ffffff', 
                        padding=(10, 5))
        style.map('Small.TButton', 
                 background=[('active', '#4a5a6a'), ('pressed', '#3a4a5a')], 
                 foreground=[('active', '#ffffff')],
                 relief=[('pressed', 'sunken'), ('!pressed', 'raised')])
        
        # 其他基本样式
        style.configure('TLabel', font=('微软雅黑', 11), background="#f0f2f5")
        style.configure('TFrame', background="#f0f2f5")
        style.configure('TEntry', font=('微软雅黑', 11))
        
        # 修改Checkbutton样式，使用默认对勾样式
        style.configure('TCheckbutton', font=('微软雅黑', 11), background="#f0f2f5")
        
        style.configure('Sash', gripcount=0, background='#c0c0c0')
        style.configure('TSeparator', background='#c0c0c0')
        style.configure('TNotebook.Tab', font=('微软雅黑', 11), padding=(10, 4))
        style.configure('Treeview', font=('微软雅黑', 11), rowheight=25)
        style.configure('Treeview.Heading', font=('微软雅黑', 11, 'bold'))
    
    def create_control_panel(self, parent):
        """创建控制面板区域"""
        control_frame = ttk.LabelFrame(parent, text="控制面板")
        control_frame.pack(fill=tk.X, padx=10, pady=3)
        
        # 使用表格布局
        control_grid = ttk.Frame(control_frame)
        control_grid.pack(padx=10, pady=5, fill=tk.X)
        
        # 第一行：按钮控制
        ttk.Label(control_grid, text="操作控制:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        button_frame = ttk.Frame(control_grid)
        button_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.select_btn = ttk.Button(button_frame, text="选择区域", 
                                     command=self.callbacks.get('select_areas', lambda: None), 
                                     style='Primary.TButton')
        self.select_btn.pack(side=tk.LEFT, padx=5)
        
        self.start_btn = ttk.Button(button_frame, text="开始监控", 
                                    command=self.callbacks.get('start_monitoring', lambda: None), 
                                    state=tk.DISABLED, 
                                    style='Primary.TButton')
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text="停止监控", 
                                   command=self.callbacks.get('stop_monitoring', lambda: None), 
                                   state=tk.DISABLED, 
                                   style='Primary.TButton')
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.test_reply_btn = ttk.Button(button_frame, text="测试回复", 
                                         command=self.callbacks.get('test_reply', lambda: None), 
                                         state=tk.DISABLED, 
                                         style='Primary.TButton')
        self.test_reply_btn.pack(side=tk.LEFT, padx=5)
        
        self.save_settings_btn = ttk.Button(button_frame, text="保存设置", 
                                           command=self.callbacks.get('save_panel_settings', lambda: None), 
                                           style='Primary.TButton')
        self.save_settings_btn.pack(side=tk.LEFT, padx=5)
        
        # 第二行：延迟设置
        ttk.Label(control_grid, text="时间设置:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        delay_frame = ttk.Frame(control_grid)
        delay_frame.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(delay_frame, text="输入延迟(秒):").pack(side=tk.LEFT)
        self.delay_var = tk.DoubleVar(value=0.1)
        self.delay_entry = ttk.Entry(delay_frame, textvariable=self.delay_var, width=6)
        self.delay_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(delay_frame, text="监控间隔(秒):").pack(side=tk.LEFT, padx=10)
        self.interval_var = tk.DoubleVar(value=1.0)
        self.interval_entry = ttk.Entry(delay_frame, textvariable=self.interval_var, width=6)
        self.interval_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(delay_frame, text="预览刷新(秒):").pack(side=tk.LEFT, padx=10)
        self.preview_interval_var = tk.DoubleVar(value=1.0)
        self.preview_interval_entry = ttk.Entry(delay_frame, textvariable=self.preview_interval_var, width=6)
        self.preview_interval_entry.pack(side=tk.LEFT, padx=5)
        
        # 添加新的时间设置行
        time_frame = ttk.Frame(control_grid)
        time_frame.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(time_frame, text="操作间隔(秒):").pack(side=tk.LEFT)
        self.mouse_interval_var = tk.DoubleVar(value=1.0)
        self.mouse_interval_entry = ttk.Entry(time_frame, textvariable=self.mouse_interval_var, width=6)
        self.mouse_interval_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(time_frame, text="等待转文字(秒):").pack(side=tk.LEFT, padx=10)
        self.voice_wait_var = tk.DoubleVar(value=6.0)
        self.voice_wait_entry = ttk.Entry(time_frame, textvariable=self.voice_wait_var, width=6)
        self.voice_wait_entry.pack(side=tk.LEFT, padx=5)
        
        # 第三行：参数设置
        ttk.Label(control_grid, text="参数设置:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        param_frame = ttk.Frame(control_grid)
        param_frame.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(param_frame, text="中心区域宽度:").pack(side=tk.LEFT)
        self.center_margin_var = tk.IntVar(value=50)
        self.center_margin_entry = ttk.Entry(param_frame, textvariable=self.center_margin_var, width=6)
        self.center_margin_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(param_frame, text="行间距阈值:").pack(side=tk.LEFT, padx=10)
        self.line_spacing_var = tk.IntVar(value=24)
        self.line_spacing_entry = ttk.Entry(param_frame, textvariable=self.line_spacing_var, width=6)
        self.line_spacing_entry.pack(side=tk.LEFT, padx=5)
        
        # 添加语音logo遮罩范围设置
        ttk.Label(param_frame, text="语音logo遮罩范围:").pack(side=tk.LEFT, padx=10)
        self.voice_logo_mask_range_var = tk.IntVar(value=20)
        self.voice_logo_mask_range_entry = ttk.Entry(param_frame, textvariable=self.voice_logo_mask_range_var, width=6)
        self.voice_logo_mask_range_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(param_frame, text="语音红点偏移:").pack(side=tk.LEFT, padx=10)
        self.voice_bubble_offset_var = tk.IntVar(value=100)
        self.voice_bubble_offset_entry = ttk.Entry(param_frame, textvariable=self.voice_bubble_offset_var, width=6)
        self.voice_bubble_offset_entry.pack(side=tk.LEFT, padx=5)
        
        # 第四行：红点设置
        ttk.Label(control_grid, text="红点设置:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        red_frame = ttk.Frame(control_grid)
        red_frame.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(red_frame, text="R:").pack(side=tk.LEFT, padx=2)
        self.red_r_var = tk.IntVar(value=220)
        self.red_r_entry = ttk.Entry(red_frame, textvariable=self.red_r_var, width=4)
        self.red_r_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(red_frame, text="G:").pack(side=tk.LEFT, padx=2)
        self.red_g_var = tk.IntVar(value=80)
        self.red_g_entry = ttk.Entry(red_frame, textvariable=self.red_g_var, width=4)
        self.red_g_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(red_frame, text="B:").pack(side=tk.LEFT, padx=2)
        self.red_b_var = tk.IntVar(value=80)
        self.red_b_entry = ttk.Entry(red_frame, textvariable=self.red_b_var, width=4)
        self.red_b_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(red_frame, text="容差:").pack(side=tk.LEFT, padx=10)
        self.red_tol_var = tk.IntVar(value=40)
        self.red_tol_entry = ttk.Entry(red_frame, textvariable=self.red_tol_var, width=4)
        self.red_tol_entry.pack(side=tk.LEFT, padx=2)
        
        # 第五行：绿色气泡遮罩设置
        ttk.Label(control_grid, text="绿色气泡:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        green_frame = ttk.Frame(control_grid)
        green_frame.grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 是否启用绿色气泡遮罩
        self.mask_green_var = tk.BooleanVar(value=True)
        self.mask_green_check = ttk.Checkbutton(green_frame, text="启用", 
                                               variable=self.mask_green_var, 
                                               command=self.callbacks.get('toggle_green_mask', lambda: None))
        self.mask_green_check.pack(side=tk.LEFT, padx=5)
        
        # 绿色RGB参数设置
        ttk.Label(green_frame, text="R:").pack(side=tk.LEFT, padx=2)
        self.green_r_var = tk.IntVar(value=80)
        self.green_r_entry = ttk.Entry(green_frame, textvariable=self.green_r_var, width=4)
        self.green_r_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(green_frame, text="G:").pack(side=tk.LEFT, padx=2)
        self.green_g_var = tk.IntVar(value=200)
        self.green_g_entry = ttk.Entry(green_frame, textvariable=self.green_g_var, width=4)
        self.green_g_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(green_frame, text="B:").pack(side=tk.LEFT, padx=2)
        self.green_b_var = tk.IntVar(value=80)
        self.green_b_entry = ttk.Entry(green_frame, textvariable=self.green_b_var, width=4)
        self.green_b_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(green_frame, text="容差:").pack(side=tk.LEFT, padx=10)
        self.green_tol_var = tk.IntVar(value=40)
        self.green_tol_entry = ttk.Entry(green_frame, textvariable=self.green_tol_var, width=4)
        self.green_tol_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(green_frame, text="最小面积:").pack(side=tk.LEFT, padx=5)
        self.green_min_area_var = tk.IntVar(value=100)
        self.green_min_area_entry = ttk.Entry(green_frame, textvariable=self.green_min_area_var, width=4)
        self.green_min_area_entry.pack(side=tk.LEFT, padx=1)
        
        # 第六行：文字颜色识别设置
        ttk.Label(control_grid, text="文字颜色:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        text_color_frame = ttk.Frame(control_grid)
        text_color_frame.grid(row=6, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 是否启用文字颜色识别
        self.text_color_enabled_var = tk.BooleanVar(value=False)
        self.text_color_check = ttk.Checkbutton(text_color_frame, text="启用", 
                                               variable=self.text_color_enabled_var, 
                                               command=self.callbacks.get('toggle_text_color', lambda: None))
        self.text_color_check.pack(side=tk.LEFT, padx=5)
        
        # 文字颜色RGB参数设置（默认白色）
        ttk.Label(text_color_frame, text="R:").pack(side=tk.LEFT, padx=2)
        self.text_color_r_var = tk.IntVar(value=255)
        self.text_color_r_entry = ttk.Entry(text_color_frame, textvariable=self.text_color_r_var, width=4)
        self.text_color_r_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(text_color_frame, text="G:").pack(side=tk.LEFT, padx=2)
        self.text_color_g_var = tk.IntVar(value=255)
        self.text_color_g_entry = ttk.Entry(text_color_frame, textvariable=self.text_color_g_var, width=4)
        self.text_color_g_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(text_color_frame, text="B:").pack(side=tk.LEFT, padx=2)
        self.text_color_b_var = tk.IntVar(value=255)
        self.text_color_b_entry = ttk.Entry(text_color_frame, textvariable=self.text_color_b_var, width=4)
        self.text_color_b_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(text_color_frame, text="容差:").pack(side=tk.LEFT, padx=10)
        self.text_color_tol_var = tk.IntVar(value=40)
        self.text_color_tol_entry = ttk.Entry(text_color_frame, textvariable=self.text_color_tol_var, width=4)
        self.text_color_tol_entry.pack(side=tk.LEFT, padx=2)
        
        # 添加分隔行
        ttk.Separator(control_grid, orient=tk.HORIZONTAL).grid(row=7, column=0, columnspan=2, sticky=tk.EW, pady=5)
        
        # 额外控制区域（移至下方）
        ttk.Label(control_grid, text="额外功能:").grid(row=8, column=0, sticky=tk.W, padx=5, pady=5)
        extra_frame = ttk.Frame(control_grid)
        extra_frame.grid(row=8, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 调试模式复选框
        self.debug_var = tk.BooleanVar(value=True)
        self.debug_check = ttk.Checkbutton(extra_frame, text="调试模式", 
                                           variable=self.debug_var, 
                                           command=self.callbacks.get('toggle_debug', lambda: None))
        self.debug_check.pack(side=tk.LEFT, padx=10)
        
        # 清除日志按钮
        self.clear_log_btn = ttk.Button(extra_frame, text="清除日志", 
                                       command=self.callbacks.get('clear_log', lambda: None), 
                                       style='Small.TButton')
        self.clear_log_btn.pack(side=tk.LEFT, padx=10)
        
        # 显示对话历史按钮
        self.show_history_btn = ttk.Button(extra_frame, text="显示对话历史", 
                                           command=self.callbacks.get('show_conversation_history', lambda: None), 
                                           style='Small.TButton')
        self.show_history_btn.pack(side=tk.LEFT, padx=10)
        
        # 测试绿色气泡遮罩按钮
        self.test_green_mask_btn = ttk.Button(extra_frame, text="测试绿色气泡遮罩", 
                                             command=self.callbacks.get('test_green_mask', lambda: None), 
                                             style='Small.TButton')
        self.test_green_mask_btn.pack(side=tk.LEFT, padx=10)
        
        # 测试文字颜色过滤按钮
        self.test_text_color_btn = ttk.Button(extra_frame, text="测试文字颜色过滤", 
                                             command=self.callbacks.get('test_text_color', lambda: None), 
                                             style='Small.TButton')
        self.test_text_color_btn.pack(side=tk.LEFT, padx=10)
        
        # 测试语音按钮06181
        self.test_voice_btn06181 = ttk.Button(extra_frame, text="测试语音", 
                                           command=self.callbacks.get('test_voice06181', lambda: None), 
                                           style='Small.TButton')
        self.test_voice_btn06181.pack(side=tk.LEFT, padx=10)
    
    def create_preview_and_log_area(self, parent):
        """创建预览和日志区域"""
        # 使用PanedWindow创建可拖动分割区域
        self.paned_window = ttk.PanedWindow(parent, orient=tk.VERTICAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 预览区（上）
        preview_frame = ttk.LabelFrame(self.paned_window, text="预览区")
        self.paned_window.add(preview_frame, weight=2)  # 预览区权重
        
        self.preview_frame = ttk.Frame(preview_frame, relief=tk.GROOVE, borderwidth=2)
        self.preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.preview_label = tk.Label(self.preview_frame, text="预览区域", bg="#e8eef7", 
                                      width=400, height=350, font=('微软雅黑', 11))
        self.preview_label.pack_propagate(False)  # 防止标签大小自动调整
        self.preview_label.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # 日志区（下）
        log_frame = ttk.LabelFrame(self.paned_window, text="日志区")
        self.paned_window.add(log_frame, weight=2)  # 修改日志区权重为和预览区相同
        
        self.log_frame = ttk.Frame(log_frame)
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=15, 
                                                 font=('Consolas', 11), 
                                                 bg="#fafbfc", fg="#222222", 
                                                 relief=tk.GROOVE, borderwidth=2)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def update_preview(self, image):
        """更新预览区域显示的图像"""
        try:
            # 如果输入是OpenCV图像，转换为PIL图像
            if isinstance(image, np.ndarray):
                img = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                img = image
            
            # 获取预览标签的实际尺寸
            self.preview_label.update()  # 确保获取最新尺寸
            preview_width = self.preview_label.winfo_width()
            preview_height = self.preview_label.winfo_height()
            
            # 如果标签尚未显示或尺寸为0，使用默认值
            if preview_width <= 1 or preview_height <= 1:
                preview_width, preview_height = 400, 250
            
            # 调整图像以保持原始比例，同时完全适应预览区域
            img_width, img_height = img.size
            width_ratio = preview_width / img_width
            height_ratio = preview_height / img_height
            
            # 选择较小的比例，确保图像完全适应预览区域不被裁剪
            scale_ratio = min(width_ratio, height_ratio)
            
            new_width = int(img_width * scale_ratio)
            new_height = int(img_height * scale_ratio)
            
            # 调整图像尺寸
            img_resized = img.resize((new_width, new_height), Image.LANCZOS)
            
            # 创建一个与预览区大小相同的空白图像，用于居中显示
            bg_img = Image.new('RGB', (preview_width, preview_height), color='#e8eef7')
            
            # 计算粘贴位置，使调整后的图像居中
            paste_x = (preview_width - new_width) // 2
            paste_y = (preview_height - new_height) // 2
            
            # 将调整后的图像粘贴到背景图像上
            bg_img.paste(img_resized, (paste_x, paste_y))
            
            img_tk = ImageTk.PhotoImage(bg_img)
            self.preview_label.config(image=img_tk)
            self.preview_label.image = img_tk
            
            return True
        except Exception as e:
            print(f"更新预览失败: {str(e)}")
            return False
    
    def add_log(self, message):
        """添加日志到日志文本框"""
        if self.log_text:
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
    
    def clear_log(self):
        """清除日志文本框内容"""
        if self.log_text:
            self.log_text.delete(1.0, tk.END)
            self.add_log("日志已清除")
    
    def enable_start_button(self, enable=True):
        """设置开始按钮的状态"""
        self.start_btn.config(state=tk.NORMAL if enable else tk.DISABLED)
    
    def enable_stop_button(self, enable=True):
        """设置停止按钮的状态"""
        self.stop_btn.config(state=tk.NORMAL if enable else tk.DISABLED)
    
    def enable_test_reply_button(self, enable=True):
        """设置测试回复按钮的状态"""
        self.test_reply_btn.config(state=tk.NORMAL if enable else tk.DISABLED)
    
    def update_monitoring_status(self, is_monitoring):
        """根据监控状态更新按钮的启用/禁用状态"""
        if is_monitoring:
            # 监控中：允许停止，禁止开始
            self.enable_start_button(False)
            self.enable_stop_button(True)
        else:
            # 未监控：允许开始，禁止停止
            self.enable_start_button(True)
            self.enable_stop_button(False)
        
    def get_settings(self):
        """获取界面上的所有设置参数"""
        return {
            'delay': self.delay_var.get(),
            'interval': self.interval_var.get(),
            'preview_interval': self.preview_interval_var.get(),
            'center_margin': self.center_margin_var.get(),
            'line_spacing': self.line_spacing_var.get(),
            'red_r': self.red_r_var.get(),
            'red_g': self.red_g_var.get(),
            'red_b': self.red_b_var.get(),
            'red_tol': self.red_tol_var.get(),
            'mask_green_bubbles': self.mask_green_var.get(),
            'green_r': self.green_r_var.get(),
            'green_g': self.green_g_var.get(),
            'green_b': self.green_b_var.get(),
            'green_tol': self.green_tol_var.get(),
            'green_min_area': self.green_min_area_var.get(),
            'debug_mode': self.debug_var.get(),
            'text_color_enabled': self.text_color_enabled_var.get(),
            'text_color_r': self.text_color_r_var.get(),
            'text_color_g': self.text_color_g_var.get(),
            'text_color_b': self.text_color_b_var.get(),
            'text_color_tol': self.text_color_tol_var.get(),
            'mouse_interval': self.mouse_interval_var.get(),
            'voice_wait': self.voice_wait_var.get(),
            'voice_logo_mask_range': self.voice_logo_mask_range_var.get(),
            'voice_bubble_offset': self.voice_bubble_offset_var.get()
        }
        
    def set_settings(self, settings):
        """将设置应用到界面控件"""
        # 时间设置
        self.delay_var.set(settings.get('delay', 0.1))
        self.interval_var.set(settings.get('interval', 1.0))
        self.preview_interval_var.set(settings.get('preview_interval', 1.0))
        
        # 参数设置
        self.center_margin_var.set(settings.get('center_margin', 50))
        self.line_spacing_var.set(settings.get('line_spacing', 24))
        
        # 红点设置
        self.red_r_var.set(settings.get('red_r', 220))
        self.red_g_var.set(settings.get('red_g', 80))
        self.red_b_var.set(settings.get('red_b', 80))
        self.red_tol_var.set(settings.get('red_tol', 40))
        
        # 绿色气泡设置
        self.mask_green_var.set(settings.get('mask_green_bubbles', True))
        self.green_r_var.set(settings.get('green_r', 80))
        self.green_g_var.set(settings.get('green_g', 200))
        self.green_b_var.set(settings.get('green_b', 80))
        self.green_tol_var.set(settings.get('green_tol', 40))
        self.green_min_area_var.set(settings.get('green_min_area', 100))
        
        # 调试模式
        self.debug_var.set(settings.get('debug_mode', True))
        
        # 文字颜色设置
        self.text_color_enabled_var.set(settings.get('text_color_enabled', False))
        self.text_color_r_var.set(settings.get('text_color_r', 255))
        self.text_color_g_var.set(settings.get('text_color_g', 255))
        self.text_color_b_var.set(settings.get('text_color_b', 255))
        self.text_color_tol_var.set(settings.get('text_color_tol', 40))
        
        # 新的时间设置
        self.mouse_interval_var.set(settings.get('mouse_interval', 1.0))
        self.voice_wait_var.set(settings.get('voice_wait', 6.0))
        self.voice_logo_mask_range_var.set(settings.get('voice_logo_mask_range', 20))
        self.voice_bubble_offset_var.set(settings.get('voice_bubble_offset', 100))

    def create_help_tab(self):
        """创建帮助选项卡"""
        help_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(help_tab, text="帮助")
        
        help_text = tk.Text(help_tab, wrap=tk.WORD, font=('微软雅黑', 11))
        help_text.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        # 添加帮助内容
        help_content = """
使用指南:
1. 点击"选择区域"按钮后，需要框选四个区域：聊天区、输入区、联系人区和联系人姓名区。
2. 画完一个区域后，按相应数字键确认：1-聊天区, 2-输入区, 3-联系人区, 4-联系人姓名区。
3. 您还可以框选多个干扰区域，每框选一个按5键确认，这些区域将被白色遮罩覆盖。
4. 所有区域选择完成后按Enter键确认，或按Esc键取消。
5. 点击"开始监控"按钮开始自动监控并回复消息。
6. 随时可以点击"停止监控"按钮暂停。

绿色气泡遮罩:
启用"绿色气泡遮罩"功能可以过滤掉自己发送的消息（通常显示为绿色气泡）。
系统会自动记录最后一个遮罩的位置，并将该位置上方的所有消息标记为"已回复"。
当切换到新联系人时，这个功能可以避免重复回复历史消息。
您可以调整颜色参数(RGB)和容差以适应不同微信主题的绿色气泡颜色。

参数说明:
- 输入延迟：模拟输入时每个字符之间的延迟(秒)
- 监控间隔：两次截图检测之间的时间间隔(秒)
- 预览刷新：预览区域刷新的时间间隔(秒)
- 中心区域宽度：聊天窗口中心区域的宽度范围(像素)
- 行间距阈值：判断多行文本是否属于同一消息的阈值(像素)
"""
        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)  # 设置为只读
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(help_tab, command=help_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        help_text.config(yscrollcommand=scrollbar.set) 
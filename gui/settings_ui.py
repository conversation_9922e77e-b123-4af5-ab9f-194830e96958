import tkinter as tk
import os
from tkinter import ttk, messagebox, filedialog
from gui.prompt_template_manager import PromptTemplateManager
from gui.chat_summary_ui06151 import ChatSummaryUI06151  # 导入聊天总结UI模块
from gui.active_dialogue_ui06242 import ActiveDialogueUI06242  # 导入主动对话UI模块
from gui.fixed_reply_ui import FixedReplyUI  # 导入固定回复UI模块

class SettingsUI:
    def __init__(self, root, callbacks):
        """
        初始化设置UI
        :param root: 父窗口
        :param callbacks: 回调函数字典
        """
        self.root = root
        self.callbacks = callbacks
        self.settings_area_tree = None
        
        # 尝试从存储加载设置
        try:
            from utils.storage import StorageManager
            storage = StorageManager()
            settings = storage.load_settings()
            
            # API设置变量
            self.provider = tk.StringVar(value=settings.get('provider', "openai"))
            self.api_key = tk.StringVar(value=settings.get('api_key', "your_openai_api_key"))
            self.api_base_url = tk.StringVar(value=settings.get('api_base_url', "https://api.openai.com"))
            self.model_name = tk.StringVar(value=settings.get('model_name', "gpt-3.5-turbo"))
            self.bot_id = tk.StringVar(value=settings.get('bot_id', ""))
            self.token = tk.StringVar(value=settings.get('token', ""))
            # AI Brain设置变量 (新增 07091)
            self.ai_brain_url07091 = tk.StringVar(value=settings.get('ai_brain_url07091', ""))
            self.ai_brain_key07091 = tk.StringVar(value=settings.get('ai_brain_key07091', ""))
            
            # 每日总结设置变量
            self.daily_summary_time = tk.StringVar(value=settings.get('daily_summary_time', "18:00"))
            self.daily_summary_prompt = tk.StringVar(value=settings.get('daily_summary_prompt', ""))
            self.mail_host = tk.StringVar(value=settings.get('mail_host', ""))
            self.mail_user = tk.StringVar(value=settings.get('mail_user', ""))
            self.mail_pass = tk.StringVar(value=settings.get('mail_pass', ""))
            self.receivers = tk.StringVar(value=settings.get('receivers', ""))
            
            # 聊天总结设置变量 (新增)
            self.accumulated_message_count06151 = tk.StringVar(value=settings.get('accumulated_message_count06151', "100"))
            self.chat_summary_prompt06151 = tk.StringVar(value=settings.get('chat_summary_prompt06151', ""))
            
            # 表情文件控制变量
            self.enable_emojis_lottery = tk.BooleanVar(value=settings.get('enable_emojis_lottery', False))
            self.emojis_lottery_rate = tk.StringVar(value=settings.get('emojis_lottery_rate', "0.5"))
            self.emojis_folder_path = tk.StringVar(value=settings.get('emojis_folder_path', ""))
            self.files_folder_path = tk.StringVar(value=settings.get('files_folder_path', ""))
            self.emojis_or_file_prompt = tk.StringVar(value=settings.get('emojis_or_file_prompt', ""))
            
            # 联系人设置变量
            self.ignored_contacts = settings.get('ignored_contacts', [])
            self.contact_filter_mode = tk.StringVar(value=settings.get('contact_filter_mode', "ignore"))
            
        except Exception as e:
            print(f"初始化设置UI时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
            # 使用默认值
            self.provider = tk.StringVar(value="openai")
            self.api_key = tk.StringVar(value="your_openai_api_key")
            self.api_base_url = tk.StringVar(value="https://api.openai.com")
            self.model_name = tk.StringVar(value="gpt-3.5-turbo")
            self.bot_id = tk.StringVar(value="")
            self.token = tk.StringVar(value="")
            # AI Brain默认值 (新增 07091)
            self.ai_brain_url07091 = tk.StringVar(value="")
            self.ai_brain_key07091 = tk.StringVar(value="")
            
            self.daily_summary_time = tk.StringVar(value="18:00")
            self.daily_summary_prompt = tk.StringVar(value="")
            self.mail_host = tk.StringVar(value="")
            self.mail_user = tk.StringVar(value="")
            self.mail_pass = tk.StringVar(value="")
            self.receivers = tk.StringVar(value="")
            
            self.accumulated_message_count06151 = tk.StringVar(value="100")
            self.chat_summary_prompt06151 = tk.StringVar(value="")
            
            self.enable_emojis_lottery = tk.BooleanVar(value=False)
            self.emojis_lottery_rate = tk.StringVar(value="0.5")
            self.emojis_folder_path = tk.StringVar(value="")
            self.files_folder_path = tk.StringVar(value="")
            self.emojis_or_file_prompt = tk.StringVar(value="")
            
            # 联系人设置变量
            self.ignored_contacts = []
            self.contact_filter_mode = tk.StringVar(value="ignore")
        
        # 提示词模板管理器
        self.prompt_manager = None
        # 聊天总结UI
        self.chat_summary_ui06151 = None
        # 主动对话UI
        self.active_dialogue_ui06242 = None
        # 固定回复UI
        self.fixed_reply_ui = None
        
    def create_settings_tab(self, parent):
        """创建设置选项卡内容"""
        # 确保parent是有效的ttk.Frame
        if not isinstance(parent, ttk.Frame):
            print(f"Warning: parent is not a ttk.Frame, it is {type(parent)}")
            
        print(f"开始创建设置选项卡，父窗口类型: {type(parent)}")
            
        # 创建主框架
        settings_frame = ttk.Frame(parent)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加标题
        title_label = ttk.Label(settings_frame, text="高级设置", font=('微软雅黑', 14, 'bold'))
        title_label.pack(pady=10)
        
        # 用Notebook创建子选项卡
        settings_notebook = ttk.Notebook(settings_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 区域设置选项卡
        area_tab = ttk.Frame(settings_notebook)
        # API设置选项卡
        api_tab = ttk.Frame(settings_notebook)
        # 提示词模板选项卡
        prompt_tab = ttk.Frame(settings_notebook)
        # 每日总结选项卡
        daily_summary_tab = ttk.Frame(settings_notebook)
        # 聊天总结选项卡 (新增)
        chat_summary_tab = ttk.Frame(settings_notebook)
        # 表情文件控制选项卡
        emoji_file_control_tab = ttk.Frame(settings_notebook)
        # 联系人设置选项卡
        ignored_contacts_tab = ttk.Frame(settings_notebook)
        # 主动对话选项卡 (新增)
        active_dialogue_tab = ttk.Frame(settings_notebook)
        # 固定回复选项卡 (新增)
        fixed_reply_tab = ttk.Frame(settings_notebook)

        # 加入选项卡
        settings_notebook.add(area_tab, text="区域设置")
        settings_notebook.add(api_tab, text="API设置")
        settings_notebook.add(prompt_tab, text="提示词模板")
        settings_notebook.add(daily_summary_tab, text="每日总结")
        settings_notebook.add(chat_summary_tab, text="聊天总结")  # 新增聊天总结选项卡
        settings_notebook.add(emoji_file_control_tab, text="表情文件控制")  # 新增表情文件控制选项卡
        settings_notebook.add(ignored_contacts_tab, text="联系人设置")  # 修改为"联系人设置"
        settings_notebook.add(active_dialogue_tab, text="主动对话")  # 新增主动对话选项卡
        settings_notebook.add(fixed_reply_tab, text="固定回复")  # 新增固定回复选项卡

        print("已创建九个子选项卡: 区域设置、API设置、提示词模板、每日总结、聊天总结、表情文件控制、联系人设置、主动对话、固定回复")
        
        # 区域设置选项卡内容
        self.create_area_settings(area_tab)
        print("区域设置选项卡内容已创建")
        
        # API设置选项卡内容
        self.create_api_settings(api_tab)
        print("API设置选项卡内容已创建")
        
        # 提示词模板选项卡内容
        self.create_prompt_settings(prompt_tab)
        print("提示词模板选项卡内容已创建")
        
        # 每日总结选项卡内容
        self.create_daily_summary_settings(daily_summary_tab)
        print("每日总结选项卡内容已创建")
        
        # 聊天总结选项卡内容 (新增)
        self.create_chat_summary_settings(chat_summary_tab)
        print("聊天总结选项卡内容已创建")
        
        # 表情文件控制选项卡内容
        self.create_emoji_file_control_settings(emoji_file_control_tab)
        print("表情文件控制选项卡内容已创建")
        
        # 联系人设置选项卡内容
        self.create_ignored_contacts_settings(ignored_contacts_tab)
        print("联系人设置选项卡内容已创建")
        
        # 主动对话选项卡内容 (新增)
        self.create_active_dialogue_settings(active_dialogue_tab)
        print("主动对话选项卡内容已创建")

        # 固定回复选项卡内容 (新增)
        self.create_fixed_reply_settings(fixed_reply_tab)
        print("固定回复选项卡内容已创建")

        # 版本信息
        version_frame = ttk.Frame(settings_frame)
        version_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        ttk.Label(version_frame, text="屏幕聊天监控助手 v1.0", font=('微软雅黑', 10)).pack(side=tk.LEFT)
        ttk.Label(version_frame, text="©2025", font=('微软雅黑', 10)).pack(side=tk.RIGHT)
        
        # 确保选项卡已选择
        settings_notebook.select(0)  # 默认选择第一个选项卡
        
        print("设置选项卡创建完成")
        
        # 返回创建的选项卡框架，以便后续引用
        return settings_frame
        
    def create_area_settings(self, parent):
        """创建区域设置内容"""
        ttk.Label(parent, text="已选择区域：").pack(anchor=tk.W, padx=10, pady=5)
        
        self.settings_area_tree = ttk.Treeview(parent, columns=("区域", "坐标"), show="headings", height=8)
        self.settings_area_tree.heading("区域", text="区域")
        self.settings_area_tree.heading("坐标", text="坐标")
        self.settings_area_tree.column("区域", width=100)
        self.settings_area_tree.column("坐标", width=300)
        self.settings_area_tree.pack(fill=tk.X, padx=10, pady=5)
        
        # 添加示例数据
        self.settings_area_tree.insert("", tk.END, values=("1.聊天区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("2.输入区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("3.联系人区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("4.名称区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("5.唯一标识区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("6.未读气泡区", "[未选择]"))
        self.settings_area_tree.insert("", tk.END, values=("7.搜索区", "[未选择]"))
        
        # 添加操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        reset_btn = ttk.Button(
            button_frame,
            text="重置区域",
            command=self.callbacks.get('reset_areas', lambda: None)
        )
        reset_btn.pack(side=tk.LEFT, padx=5)
        
        select_btn = ttk.Button(
            button_frame,
            text="选择区域",
            command=self.callbacks.get('select_areas', lambda: None)
        )
        select_btn.pack(side=tk.LEFT, padx=5)
        
    def create_api_settings(self, parent):
        """创建API设置内容"""
        api_frame = ttk.Frame(parent)
        api_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加服务提供商选择
        provider_frame = ttk.Frame(api_frame)
        provider_frame.pack(anchor=tk.W, fill=tk.X, padx=5, pady=10)
        
        ttk.Label(provider_frame, text="选择服务提供商:", font=('微软雅黑', 11, 'bold')).pack(side=tk.LEFT, padx=5)
        
        # OpenAI选项
        self.openai_radio = ttk.Radiobutton(
            provider_frame, 
            text="OpenAI", 
            variable=self.provider, 
            value="openai",
            command=self.update_provider_state
        )
        self.openai_radio.pack(side=tk.LEFT, padx=15)
        
        # Coze Space选项
        self.coze_radio = ttk.Radiobutton(
            provider_frame, 
            text="Coze Space", 
            variable=self.provider, 
            value="coze",
            command=self.update_provider_state
        )
        self.coze_radio.pack(side=tk.LEFT, padx=15)
        
        # AI Brain选项 (新增)
        self.ai_brain_radio = ttk.Radiobutton(
            provider_frame,
            text="AI Brain",
            variable=self.provider,
            value="ai_brain",
            command=self.update_provider_state
        )
        self.ai_brain_radio.pack(side=tk.LEFT, padx=15)
        
        # 创建设置内容框架 - 用于容纳两种设置框架
        settings_container = ttk.Frame(api_frame)
        settings_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加说明文字
        explanation_text = "选择OpenAI后提示词模板、每日总结、聊天总结、表情文件控制、主动对话功能均使用OpenAI接口。\n\n选择Coze Space后，提示词模板、每日总结、聊天总结、主动对话功能依然使用OpenAI接口，仅回复使用coze，并且表情文件控制会失效。\n\n选择AI Brain后，AI大脑拥有非常强悍的聊天控场的能力，可以通过云端WEB配置各种虚拟角色、场景、触发器来控制聊天节奏，支持自动从聊天语意中提取数据。选择以后本软件自带的提示词模板将失效，使用AI Brain更加强力的场景系统。其他功能不受影响"
        explanation_frame = ttk.LabelFrame(api_frame, text="功能说明")
        explanation_frame.pack(fill=tk.X, padx=5, pady=10, before=settings_container)
        explanation_label = ttk.Label(explanation_frame, text=explanation_text, wraplength=600, justify=tk.LEFT)
        explanation_label.pack(padx=10, pady=10, anchor=tk.W)
        
        # 创建OpenAI设置框架
        self.openai_settings_frame = ttk.LabelFrame(settings_container, text="OpenAI 设置")
        self.openai_settings_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # OpenAI API Base URL 设置
        ttk.Label(self.openai_settings_frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        api_url_entry = ttk.Entry(self.openai_settings_frame, textvariable=self.api_base_url, width=50)
        api_url_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # OpenAI API Key 设置
        ttk.Label(self.openai_settings_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(self.openai_settings_frame, textvariable=self.api_key, width=50, show="*")
        api_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏API Key按钮
        self.show_api_key = tk.BooleanVar(value=False)
        show_key_check = ttk.Checkbutton(
            self.openai_settings_frame, 
            text="显示", 
            variable=self.show_api_key,
            command=lambda: api_key_entry.config(show="" if self.show_api_key.get() else "*")
        )
        show_key_check.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 模型名称设置 - 改回普通输入框
        ttk.Label(self.openai_settings_frame, text="模型名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        model_name_entry = ttk.Entry(self.openai_settings_frame, textvariable=self.model_name, width=50)
        model_name_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 创建Coze Space设置框架
        self.coze_settings_frame = ttk.LabelFrame(settings_container, text="Coze Space 设置")
        self.coze_settings_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # Bot ID设置
        ttk.Label(self.coze_settings_frame, text="Bot ID:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        bot_id_entry = ttk.Entry(self.coze_settings_frame, textvariable=self.bot_id, width=50)
        bot_id_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Token设置
        ttk.Label(self.coze_settings_frame, text="Token:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        token_entry = ttk.Entry(self.coze_settings_frame, textvariable=self.token, width=50, show="*")
        token_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏Token按钮
        self.show_token = tk.BooleanVar(value=False)
        show_token_check = ttk.Checkbutton(
            self.coze_settings_frame, 
            text="显示", 
            variable=self.show_token,
            command=lambda: token_entry.config(show="" if self.show_token.get() else "*")
        )
        show_token_check.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 创建AI Brain设置框架 (新增 07091)
        self.ai_brain_settings_frame = ttk.LabelFrame(settings_container, text="AI Brain 设置")
        self.ai_brain_settings_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # AI Brain API URL设置
        ttk.Label(self.ai_brain_settings_frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ai_brain_url_entry = ttk.Entry(self.ai_brain_settings_frame, textvariable=self.ai_brain_url07091, width=50)
        ai_brain_url_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # AI Brain API Key设置
        ttk.Label(self.ai_brain_settings_frame, text="API Key:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ai_brain_key_entry = ttk.Entry(self.ai_brain_settings_frame, textvariable=self.ai_brain_key07091, width=50, show="*")
        ai_brain_key_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏AI Brain API Key按钮
        self.show_ai_brain_key = tk.BooleanVar(value=False)
        show_ai_brain_key_check = ttk.Checkbutton(
            self.ai_brain_settings_frame, 
            text="显示", 
            variable=self.show_ai_brain_key,
            command=lambda: ai_brain_key_entry.config(show="" if self.show_ai_brain_key.get() else "*")
        )
        show_ai_brain_key_check.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # 保存按钮 - 移到所有设置框架之后的固定位置
        button_frame = ttk.Frame(api_frame)
        button_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=5, pady=10)
        
        save_btn = ttk.Button(button_frame, text="保存设置", 
                             command=self.save_api_settings, 
                             style='Primary.TButton')
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 初始化界面状态
        self.update_provider_state()
        
    def update_provider_state(self):
        """更新提供商选择状态"""
        provider = self.provider.get()
        
        if provider == "openai":
            # 显示OpenAI设置，隐藏其他设置
            self.openai_settings_frame.pack(fill=tk.X, padx=5, pady=10)
            self.coze_settings_frame.pack_forget()
            self.ai_brain_settings_frame.pack_forget()  # 隐藏AI Brain设置
        elif provider == "coze":
            # 显示Coze设置，隐藏其他设置
            self.openai_settings_frame.pack_forget()
            self.coze_settings_frame.pack(fill=tk.X, padx=5, pady=10)
            self.ai_brain_settings_frame.pack_forget()  # 隐藏AI Brain设置
        elif provider == "ai_brain":
            # 显示AI Brain设置，隐藏其他设置
            self.openai_settings_frame.pack_forget()
            self.coze_settings_frame.pack_forget()
            self.ai_brain_settings_frame.pack(fill=tk.X, padx=5, pady=10)
        else:
            # 默认显示OpenAI设置
            self.openai_settings_frame.pack(fill=tk.X, padx=5, pady=10)
            self.coze_settings_frame.pack_forget()
            self.ai_brain_settings_frame.pack_forget()
        
    def create_prompt_settings(self, parent):
        """创建提示词模板设置内容"""
        print(f"开始创建提示词模板设置，父窗口类型: {type(parent)}")
        
        # 创建容器框架
        prompt_container = ttk.Frame(parent)
        prompt_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        print(f"已创建提示词模板容器框架")
        
        # 创建提示词模板管理器, 使用容器框架作为父容器
        self.prompt_manager = PromptTemplateManager(prompt_container, self.save_prompt_templates)
        
        print(f"已创建PromptTemplateManager实例: {self.prompt_manager}")
        
        # 立即加载模板数据
        try:
            from utils.storage import StorageManager
            storage = StorageManager()
            settings = storage.load_settings()
            print(f"从存储加载设置: {settings.keys() if settings else 'None'}")
            
            if settings and 'prompt_templates' in settings:
                print(f"找到 {len(settings['prompt_templates'])} 个提示词模板")
                self.prompt_manager.load_templates(settings)
                print(f"已加载模板到prompt_manager")
            else:
                print("未在设置中找到提示词模板")
        except Exception as e:
            print(f"加载提示词模板时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
        # 由于PromptTemplateManager在初始化时已经将frame放入父容器，此处不需要额外操作
        # 可以链式调用其他方法进行初始化
        
        print(f"提示词模板设置创建完成")

    def save_prompt_templates(self, templates_data):
        """保存提示词模板配置"""
        if 'save_prompt_templates' in self.callbacks:
            self.callbacks['save_prompt_templates'](templates_data)
        else:
            print("Warning: 'save_prompt_templates' 回调函数未定义")
        
    def save_api_settings(self):
        """保存API设置"""
        # 调用回调函数保存设置
        if 'save_api_settings' in self.callbacks:
            self.callbacks['save_api_settings'](
                self.get_api_settings()
            )
            messagebox.showinfo("保存成功", "API设置已保存")
        
    def create_daily_summary_settings(self, parent):
        """创建每日总结设置内容"""
        summary_frame = ttk.Frame(parent)
        summary_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加标题
        ttk.Label(summary_frame, text="每日总结设置", font=('微软雅黑', 11, 'bold')).pack(anchor=tk.W, padx=5, pady=10)
        
        # 每日总结时间设置
        time_frame = ttk.Frame(summary_frame)
        time_frame.pack(fill=tk.X, padx=5, pady=5, anchor=tk.W)
        
        ttk.Label(time_frame, text="总结时间:").pack(side=tk.LEFT, padx=5)
        time_entry = ttk.Entry(time_frame, textvariable=self.daily_summary_time, width=10)
        time_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(time_frame, text="(格式: HH:MM，例如 18:30)").pack(side=tk.LEFT, padx=5)
        
        # 总结提示词设置
        prompt_frame = ttk.LabelFrame(summary_frame, text="总结提示词")
        prompt_frame.pack(fill=tk.X, padx=5, pady=10)
        
        prompt_text = tk.Text(prompt_frame, height=10, width=60)
        prompt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 获取当前提示词
        current_prompt = self.daily_summary_prompt.get()
        
        # 如果没有提示词，设置默认值
        if not current_prompt:
            default_prompt = """你是一个聊天总结专家，有这丰富的识别问题和提炼要点的能力。

请对以下聊天对话内容进行总结：
{chat_content}

总结输出格式：
1.用户主要的疑问和顾虑
2.用户的性格和情绪
3.用户的意向
4.整体总结"""
            current_prompt = default_prompt
            self.daily_summary_prompt.set(default_prompt)
        
        # 设置文本框内容
        prompt_text.insert("1.0", current_prompt)
            
        # 绑定文本变化事件
        def on_prompt_change(event=None):
            self.daily_summary_prompt.set(prompt_text.get("1.0", "end-1c"))
        prompt_text.bind("<KeyRelease>", on_prompt_change)
        
        # 邮件设置框架
        email_frame = ttk.LabelFrame(summary_frame, text="邮件提醒配置")
        email_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # SMTP服务器
        ttk.Label(email_frame, text="邮件服务器:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(email_frame, textvariable=self.mail_host, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 邮箱账号
        ttk.Label(email_frame, text="邮箱账号:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(email_frame, textvariable=self.mail_user, width=40).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 邮箱密码
        ttk.Label(email_frame, text="邮箱密码:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        pass_entry = ttk.Entry(email_frame, textvariable=self.mail_pass, width=40, show="*")
        pass_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 显示/隐藏密码按钮
        self.show_mail_pass = tk.BooleanVar(value=False)
        show_pass_check = ttk.Checkbutton(
            email_frame, 
            text="显示", 
            variable=self.show_mail_pass,
            command=lambda: pass_entry.config(show="" if self.show_mail_pass.get() else "*")
        )
        show_pass_check.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 收件人
        ttk.Label(email_frame, text="收件人:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(email_frame, textvariable=self.receivers, width=40).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(email_frame, text="(多个收件人用逗号分隔)").grid(row=3, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(summary_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=15)
        
        # 立即总结按钮
        summary_now_btn = ttk.Button(
            button_frame, 
            text="立即总结", 
            command=self.callbacks.get('run_summary_now', lambda: None)
        )
        summary_now_btn.pack(side=tk.LEFT, padx=5)
        
        # 保存设置按钮
        save_btn = ttk.Button(
            button_frame, 
            text="保存设置", 
            command=self.save_daily_summary_settings
        )
        save_btn.pack(side=tk.LEFT, padx=5)
        
    def save_daily_summary_settings(self):
        """保存每日总结设置"""
        settings = {
            'daily_summary_time': self.daily_summary_time.get(),
            'daily_summary_prompt': self.daily_summary_prompt.get(),
            'mail_host': self.mail_host.get(),
            'mail_user': self.mail_user.get(),
            'mail_pass': self.mail_pass.get(),
            'receivers': self.receivers.get()
        }
        if self.callbacks.get('save_daily_summary_settings'):
            self.callbacks['save_daily_summary_settings'](settings)
            messagebox.showinfo("保存成功", "每日总结设置已保存")
        
    def update_area_tree(self, areas):
        """更新区域树显示"""
        if not self.settings_area_tree:
            return
            
        # 清空现有条目
        for item in self.settings_area_tree.get_children():
            self.settings_area_tree.delete(item)
        
        # 添加基本区域
        self.settings_area_tree.insert("", tk.END, values=("1.聊天区", str(areas.get('chat_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("2.输入区", str(areas.get('input_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("3.联系人区", str(areas.get('contact_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("4.名称区", str(areas.get('contact_name_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("5.唯一标识区", str(areas.get('unique_id_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("6.未读气泡区", str(areas.get('unread_bubble_area', '[未选择]'))))
        self.settings_area_tree.insert("", tk.END, values=("7.搜索区", str(areas.get('search_area', '[未选择]'))))
        
        # 添加干扰区
        noise_areas = areas.get('noise_areas', [])
        if noise_areas:
            for i, noise_area in enumerate(noise_areas):
                self.settings_area_tree.insert("", tk.END, values=(f"8.干扰区{i+1}", str(noise_area)))
        else:
            self.settings_area_tree.insert("", tk.END, values=("8.干扰区", "[未选择]"))
        
    def create_chat_summary_settings(self, parent):
        """创建聊天总结设置内容"""
        # 创建聊天总结UI
        chat_summary_callbacks = {
            'save_chat_summary_settings': self.save_chat_summary_settings,
            'generate_chat_summary': self.callbacks.get('generate_chat_summary', lambda contact_name: None)
        }
        self.chat_summary_ui06151 = ChatSummaryUI06151(parent, chat_summary_callbacks)
        
        # 设置初始值
        settings = {
            'accumulated_message_count06151': self.accumulated_message_count06151.get(),
            'chat_summary_prompt06151': self.chat_summary_prompt06151.get()
        }
        self.chat_summary_ui06151.set_chat_summary_settings(settings)
    
    def save_chat_summary_settings(self, settings):
        """保存聊天总结设置"""
        # 更新变量
        self.accumulated_message_count06151.set(settings.get('accumulated_message_count06151', '100'))
        self.chat_summary_prompt06151.set(settings.get('chat_summary_prompt06151', ''))
        
        # 调用回调
        if self.callbacks.get('save_chat_summary_settings'):
            self.callbacks['save_chat_summary_settings'](settings)
    
    def get_chat_summary_settings(self):
        """获取聊天总结设置"""
        return {
            'accumulated_message_count06151': self.accumulated_message_count06151.get(),
            'chat_summary_prompt06151': self.chat_summary_prompt06151.get()
        }
        
    def set_api_settings(self, settings):
        """
        设置API参数
        :param settings: 设置字典
        """
        # 设置API参数
        self.provider.set(settings.get('provider', 'openai'))
        self.api_key.set(settings.get('api_key', ''))
        self.api_base_url.set(settings.get('api_base_url', 'https://api.openai.com'))
        self.model_name.set(settings.get('model_name', 'gpt-3.5-turbo'))
        self.bot_id.set(settings.get('bot_id', ''))
        self.token.set(settings.get('token', ''))
        # AI Brain设置 (新增 07091)
        self.ai_brain_url07091.set(settings.get('ai_brain_url07091', ''))
        self.ai_brain_key07091.set(settings.get('ai_brain_key07091', ''))
        
        # 设置每日总结参数
        self.daily_summary_time.set(settings.get('daily_summary_time', '18:00'))
        self.daily_summary_prompt.set(settings.get('daily_summary_prompt', ''))
        self.mail_host.set(settings.get('mail_host', ''))
        self.mail_user.set(settings.get('mail_user', ''))
        self.mail_pass.set(settings.get('mail_pass', ''))
        self.receivers.set(settings.get('receivers', ''))
        
        # 设置聊天总结参数 (新增)
        self.accumulated_message_count06151.set(settings.get('accumulated_message_count06151', '100'))
        self.chat_summary_prompt06151.set(settings.get('chat_summary_prompt06151', ''))
        
        # 设置表情文件控制参数
        self.enable_emojis_lottery.set(settings.get('enable_emojis_lottery', False))
        self.emojis_lottery_rate.set(settings.get('emojis_lottery_rate', '0.5'))
        self.emojis_folder_path.set(settings.get('emojis_folder_path', ''))
        self.files_folder_path.set(settings.get('files_folder_path', ''))
        self.emojis_or_file_prompt.set(settings.get('emojis_or_file_prompt', ''))
        
        # 设置联系人设置参数
        self.ignored_contacts = settings.get('ignored_contacts', [])
        self.contact_filter_mode.set(settings.get('contact_filter_mode', 'ignore'))
        
        # 更新聊天总结UI
        if self.chat_summary_ui06151:
            self.chat_summary_ui06151.set_chat_summary_settings({
                'accumulated_message_count06151': self.accumulated_message_count06151.get(),
                'chat_summary_prompt06151': self.chat_summary_prompt06151.get()
            })
        
        # 只有在openai_settings_frame存在时才更新界面状态
        if hasattr(self, 'openai_settings_frame') and self.openai_settings_frame:
            self.update_provider_state()
        
        # 如果有提示词模板，设置提示词模板管理器
        if self.prompt_manager and 'prompt_templates' in settings:
            self.prompt_manager.load_templates(settings)
            
    def get_api_settings(self):
        """获取API设置"""
        provider = self.provider.get()
        
        settings = {
            'provider': provider,
            'system_prompt': "你是一个有用的助手。请用简短、友好的方式回答问题。",  # 默认系统提示词
            'delay': 0.1  # 默认延迟
        }
        
        # 根据不同提供商添加特定设置
        if provider == 'openai':
            settings.update({
                'api_key': self.api_key.get(),
                'api_base_url': self.api_base_url.get(),
                'model_name': self.model_name.get(),
            })
        elif provider == 'coze':
            settings.update({
                'bot_id': self.bot_id.get(),
                'token': self.token.get(),
            })
        elif provider == 'ai_brain':
            settings.update({
                'ai_brain_url07091': self.ai_brain_url07091.get(),
                'ai_brain_key07091': self.ai_brain_key07091.get(),
            })
            
        # 添加每日总结设置
        settings.update({
            'daily_summary_time': self.daily_summary_time.get(),
            'daily_summary_prompt': self.daily_summary_prompt.get(),
            'mail_host': self.mail_host.get(),
            'mail_user': self.mail_user.get(),
            'mail_pass': self.mail_pass.get(),
            'receivers': self.receivers.get()
        })
        
        # 添加聊天总结设置 (新增)
        settings.update({
            'accumulated_message_count06151': self.accumulated_message_count06151.get(),
            'chat_summary_prompt06151': self.chat_summary_prompt06151.get()
        })
        
        # 添加表情文件控制设置
        settings.update({
            'enable_emojis_lottery': self.enable_emojis_lottery.get(),
            'emojis_lottery_rate': self.emojis_lottery_rate.get(),
            'emojis_folder_path': self.emojis_folder_path.get(),
            'files_folder_path': self.files_folder_path.get(),
            'emojis_or_file_prompt': self.emojis_or_file_prompt.get()
        })
        
        # 添加联系人设置
        settings.update({
            'ignored_contacts': self.ignored_contacts,
            'contact_filter_mode': self.contact_filter_mode.get()
        })
        
        # 添加提示词模板（如果有）
        if hasattr(self, 'prompt_manager') and self.prompt_manager:
            settings['prompt_templates'] = self.prompt_manager.prompt_templates
            
        return settings
        
    def get_system_prompt_for_contact(self, contact_name):
        """根据联系人名称获取系统提示词"""
        print(f"SettingsUI.get_system_prompt_for_contact被调用，联系人: {contact_name}")
        
        if hasattr(self, 'prompt_manager'):
            if self.prompt_manager:
                print(f"prompt_manager存在，将调用prompt_manager.get_system_prompt")
                try:
                    prompt = self.prompt_manager.get_system_prompt(contact_name)
                    # 确保prompt不为None
                    if prompt is None:
                        print("警告：prompt_manager返回的prompt为None，将其设置为空字符串")
                        prompt = ""
                    print(f"prompt_manager返回的提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
                    return prompt
                except Exception as e:
                    print(f"从prompt_manager获取系统提示词时出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
            else:
                print("警告: prompt_manager属性存在但为None")
        else:
            print("警告: 未创建prompt_manager属性")
            
        # 如果没有prompt_manager或发生错误，返回空提示词
        print("无法获取提示词，返回空字符串")
        return ""

    def get_daily_summary_settings(self):
        """
        获取每日总结设置
        :return: 每日总结设置字典
        """
        return {
            'daily_summary_time': self.daily_summary_time.get(),
            'daily_summary_prompt': self.daily_summary_prompt.get(),
            'mail_host': self.mail_host.get(),
            'mail_user': self.mail_user.get(),
            'mail_pass': self.mail_pass.get(),
            'receivers': self.receivers.get()
        }

    def create_emoji_file_control_settings(self, parent):
        """创建表情文件控制设置内容"""
        container = ttk.Frame(parent)
        container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 表情发送控制
        emoji_control_frame = ttk.LabelFrame(container, text="表情发送控制")
        emoji_control_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 1. 是否开启表情发送
        enable_frame = ttk.Frame(emoji_control_frame)
        enable_frame.pack(fill=tk.X, padx=5, pady=5)
        enable_checkbox = ttk.Checkbutton(
            enable_frame, 
            text="开启表情发送功能", 
            variable=self.enable_emojis_lottery
        )
        enable_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 2. 表情发送概率
        rate_frame = ttk.Frame(emoji_control_frame)
        rate_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(rate_frame, text="表情发送概率:").pack(side=tk.LEFT, padx=5)
        rate_entry = ttk.Entry(rate_frame, textvariable=self.emojis_lottery_rate, width=10)
        rate_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(rate_frame, text="(0-1之间的小数，如0.5表示50%概率)").pack(side=tk.LEFT, padx=5)
        
        # 文件夹设置
        folder_frame = ttk.LabelFrame(container, text="文件夹设置")
        folder_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 3. 表情文件夹路径
        emoji_folder_frame = ttk.Frame(folder_frame)
        emoji_folder_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(emoji_folder_frame, text="表情文件夹路径:").pack(side=tk.LEFT, padx=5)
        emoji_folder_entry = ttk.Entry(emoji_folder_frame, textvariable=self.emojis_folder_path, width=40)
        emoji_folder_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        def select_emoji_folder():
            folder_path = filedialog.askdirectory(title="选择表情文件夹")
            if folder_path:
                self.emojis_folder_path.set(folder_path)
        
        emoji_folder_btn = ttk.Button(emoji_folder_frame, text="选择文件夹", command=select_emoji_folder)
        emoji_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 4. 文件文件夹路径
        file_folder_frame = ttk.Frame(folder_frame)
        file_folder_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(file_folder_frame, text="文件文件夹路径:").pack(side=tk.LEFT, padx=5)
        file_folder_entry = ttk.Entry(file_folder_frame, textvariable=self.files_folder_path, width=40)
        file_folder_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        def select_file_folder():
            folder_path = filedialog.askdirectory(title="选择文件文件夹")
            if folder_path:
                self.files_folder_path.set(folder_path)
        
        file_folder_btn = ttk.Button(file_folder_frame, text="选择文件夹", command=select_file_folder)
        file_folder_btn.pack(side=tk.LEFT, padx=5)
        
        # 提示词设置
        prompt_frame = ttk.LabelFrame(container, text="提示词设置")
        prompt_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)
        
        # 5. 自动生成提示词按钮
        auto_prompt_frame = ttk.Frame(prompt_frame)
        auto_prompt_frame.pack(fill=tk.X, padx=5, pady=5)
        
        def generate_prompt():
            emoji_folder = self.emojis_folder_path.get()
            file_folder = self.files_folder_path.get()
            
            if not emoji_folder and not file_folder:
                messagebox.showwarning("警告", "请先选择表情文件夹和文件文件夹")
                return
            
            emoji_files = []
            file_files = []
            
            try:
                if emoji_folder and os.path.exists(emoji_folder):
                    # 遍历表情文件夹及其子目录
                    for root, _, files in os.walk(emoji_folder):
                        for f in files:
                            full_path = os.path.join(root, f)
                            if os.path.isfile(full_path):
                                # 存储相对路径
                                rel_path = os.path.relpath(full_path, emoji_folder)
                                emoji_files.append(rel_path)
                
                if file_folder and os.path.exists(file_folder):
                    # 遍历文件文件夹及其子目录
                    for root, _, files in os.walk(file_folder):
                        for f in files:
                            full_path = os.path.join(root, f)
                            if os.path.isfile(full_path):
                                # 存储相对路径
                                rel_path = os.path.relpath(full_path, file_folder)
                                file_files.append(rel_path)
                
                emoji_list = "', '".join(emoji_files)
                file_list = "', '".join(file_files)
                
                prompt = f"请帮我根据现在的聊天场景，如果需要则在聊天中发送一个表情或文件。请从以下文件中选择一个，然后使用send_emojis或send_file工具发送它：\n"
                
                if emoji_files:
                    prompt += f"表情有：'{emoji_list}'。\n"
                
                if file_files:
                    prompt += f"文件有：'{file_list}'\n"
                
                prompt += "\n\n请根据聊天上下文选择一个适合的文件发送。发送时请使用文件的完整路径。如果不需要发送，则不需要调用工具"
                
                self.emojis_or_file_prompt.set(prompt)
                # 更新文本框内容
                prompt_text.delete("1.0", tk.END)
                prompt_text.insert(tk.END, prompt)
                messagebox.showinfo("成功", "提示词已自动生成")
            except Exception as e:
                messagebox.showerror("错误", f"生成提示词时出错: {str(e)}")
        
        auto_prompt_btn = ttk.Button(auto_prompt_frame, text="自动生成提示词", command=generate_prompt)
        auto_prompt_btn.pack(side=tk.LEFT, padx=5)
        
        # 6. 表情文件提示词
        prompt_text_frame = ttk.Frame(prompt_frame)
        prompt_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        ttk.Label(prompt_text_frame, text="表情文件提示词:").pack(anchor=tk.W, padx=5, pady=2)
        
        prompt_text = tk.Text(prompt_text_frame, wrap=tk.WORD, height=10)
        prompt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        prompt_text.insert(tk.END, self.emojis_or_file_prompt.get())
        
        # 设置文本框与变量的绑定
        def update_prompt_var(event=None):
            self.emojis_or_file_prompt.set(prompt_text.get("1.0", tk.END).strip())
        
        prompt_text.bind("<KeyRelease>", update_prompt_var)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(prompt_text, command=prompt_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        prompt_text.config(yscrollcommand=scrollbar.set)
        
        # 7. 保存设置按钮
        btn_frame = ttk.Frame(container)
        btn_frame.pack(fill=tk.X, padx=5, pady=10)
        
        save_btn = ttk.Button(
            btn_frame, 
            text="保存设置", 
            command=self.save_emoji_file_control_settings
        )
        save_btn.pack(side=tk.RIGHT, padx=5)
        
    def save_emoji_file_control_settings(self):
        """保存表情文件控制设置"""
        settings = {
            'enable_emojis_lottery': self.enable_emojis_lottery.get(),
            'emojis_lottery_rate': self.emojis_lottery_rate.get(),
            'emojis_folder_path': self.emojis_folder_path.get(),
            'files_folder_path': self.files_folder_path.get(),
            'emojis_or_file_prompt': self.emojis_or_file_prompt.get()
        }
        
        # 调用回调
        if self.callbacks.get('save_settings'):
            self.callbacks['save_settings'](settings)
            messagebox.showinfo("成功", "表情文件控制设置已保存")
        else:
            print("警告: 没有保存设置的回调函数")
            messagebox.showwarning("警告", "无法保存设置，缺少回调函数")
            
    def get_emoji_file_control_settings(self):
        """获取表情文件控制设置"""
        return {
            'enable_emojis_lottery': self.enable_emojis_lottery.get(),
            'emojis_lottery_rate': self.emojis_lottery_rate.get(),
            'emojis_folder_path': self.emojis_folder_path.get(),
            'files_folder_path': self.files_folder_path.get(),
            'emojis_or_file_prompt': self.emojis_or_file_prompt.get()
        }

    def create_ignored_contacts_settings(self, parent):
        """创建联系人设置内容"""
        container = ttk.Frame(parent)
        container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加标题
        title_frame = ttk.Frame(container)
        title_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(
            title_frame, 
            text="联系人设置", 
            font=('微软雅黑', 12, 'bold')
        ).pack(side=tk.LEFT, padx=5)
        
        # 添加模式选择
        mode_frame = ttk.LabelFrame(container, text="筛选模式")
        mode_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加单选按钮组
        ignore_radio = ttk.Radiobutton(
            mode_frame,
            text="不理睬模式 (符合条件的联系人不会收到回复)",
            variable=self.contact_filter_mode,
            value="ignore"
        )
        ignore_radio.pack(anchor=tk.W, padx=10, pady=5)
        
        reply_only_radio = ttk.Radiobutton(
            mode_frame,
            text="仅回复模式 (只有符合条件的联系人才会收到回复)",
            variable=self.contact_filter_mode,
            value="reply_only"
        )
        reply_only_radio.pack(anchor=tk.W, padx=10, pady=5)
        
        ttk.Label(
            mode_frame,
            text="(筛选使用关键字包含匹配，如设置\"小明\"将匹配\"小明的朋友\")", 
            font=('微软雅黑', 10)
        ).pack(anchor=tk.W, padx=10, pady=5)
        
        # 创建联系人列表框架
        list_frame = ttk.LabelFrame(container, text="联系人列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=10)
        
        # 创建表格和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 使用Treeview显示联系人列表
        self.ignored_contacts_tree = ttk.Treeview(
            list_container,
            columns=("勾选", "联系人名称"),
            show="headings",
            height=10
        )
        self.ignored_contacts_tree.heading("勾选", text="勾选")
        self.ignored_contacts_tree.heading("联系人名称", text="联系人名称")
        
        # 调整列宽
        self.ignored_contacts_tree.column("勾选", width=50, anchor=tk.CENTER)
        self.ignored_contacts_tree.column("联系人名称", width=300)
        
        self.ignored_contacts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.ignored_contacts_tree.yview)
        self.ignored_contacts_tree.configure(yscrollcommand=scrollbar.set)
        
        # 绑定点击事件以切换勾选状态
        self.ignored_contacts_tree.bind("<ButtonRelease-1>", self.toggle_ignored_contact)
        
        # 加载联系人按钮框架
        load_frame = ttk.Frame(container)
        load_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 加载联系人按钮
        load_btn = ttk.Button(
            load_frame,
            text="从聊天历史加载联系人",
            command=self.load_contacts_from_history
        )
        load_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建全选按钮和其勾选状态
        self.select_all_ignored_var = tk.BooleanVar()
        select_all_checkbox = ttk.Checkbutton(
            load_frame,
            text="全选",
            variable=self.select_all_ignored_var,
            command=self.toggle_select_all_ignored
        )
        select_all_checkbox.pack(side=tk.LEFT, padx=5)
        
        # 添加操作按钮框架
        button_frame = ttk.Frame(container)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 添加新联系人输入框和按钮
        ttk.Label(button_frame, text="联系人名称:").pack(side=tk.LEFT, padx=5)
        self.new_contact_entry = ttk.Entry(button_frame, width=30)
        self.new_contact_entry.pack(side=tk.LEFT, padx=5)
        
        # 添加联系人按钮
        add_btn = ttk.Button(
            button_frame, 
            text="添加", 
            command=self.add_ignored_contact
        )
        add_btn.pack(side=tk.LEFT, padx=5)
        
        # 删除联系人按钮
        delete_btn = ttk.Button(
            button_frame, 
            text="删除所选", 
            command=self.delete_ignored_contact
        )
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        # 保存按钮框架
        save_frame = ttk.Frame(container)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        
        # 保存设置按钮
        save_btn = ttk.Button(
            save_frame, 
            text="保存设置", 
            command=self.save_ignored_contacts_settings
        )
        save_btn.pack(side=tk.RIGHT, padx=5)
        
        # 初始填充列表
        self.update_ignored_contacts_list()
        
    def load_contacts_from_history(self):
        """从聊天历史目录中加载联系人名称"""
        try:
            # 加载聊天记录
            from utils.storage import StorageManager
            storage = StorageManager()
            conversations, _ = storage.load_all_conversations()
            
            # 获取联系人名称列表
            contact_names = list(conversations.keys())
            
            # 更新联系人树形列表
            self.update_ignored_contacts_tree(contact_names)
            
            messagebox.showinfo("成功", f"已从聊天历史中加载 {len(contact_names)} 个联系人")
        except Exception as e:
            print(f"从聊天历史加载联系人时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            messagebox.showerror("错误", f"从聊天历史加载联系人时出错: {str(e)}")
    
    def update_ignored_contacts_tree(self, contact_names):
        """更新联系人树形列表"""
        # 清空现有项目
        for item in self.ignored_contacts_tree.get_children():
            self.ignored_contacts_tree.delete(item)
        
        # 添加联系人到树形列表
        for contact_name in contact_names:
            # 如果contact_name在self.ignored_contacts列表中，则勾选
            check_mark = "√" if contact_name in self.ignored_contacts else ""
            
            self.ignored_contacts_tree.insert("", tk.END, values=(
                check_mark,
                contact_name
            ))
    
    def toggle_ignored_contact(self, event):
        """切换勾选状态"""
        region = self.ignored_contacts_tree.identify("region", event.x, event.y)
        if region != "cell":
            return
            
        column = self.ignored_contacts_tree.identify_column(event.x)
        if column != "#1":  # 只处理第一列（勾选列）
            return
            
        item = self.ignored_contacts_tree.identify("item", event.x, event.y)
        if not item:
            return
            
        values = self.ignored_contacts_tree.item(item, "values")
        contact_name = values[1]
        is_checked = values[0] == "√"
        
        # 切换勾选状态
        new_check = "" if is_checked else "√"
        self.ignored_contacts_tree.item(item, values=(new_check, contact_name))
        
        # 更新ignored_contacts列表
        if new_check == "√" and contact_name not in self.ignored_contacts:
            self.ignored_contacts.append(contact_name)
        elif new_check == "" and contact_name in self.ignored_contacts:
            self.ignored_contacts.remove(contact_name)
    
    def toggle_select_all_ignored(self):
        """全选/取消全选所有联系人"""
        is_select_all = self.select_all_ignored_var.get()
        
        for item in self.ignored_contacts_tree.get_children():
            values = self.ignored_contacts_tree.item(item, "values")
            contact_name = values[1]
            
            # 更新勾选状态
            new_check = "√" if is_select_all else ""
            self.ignored_contacts_tree.item(item, values=(new_check, contact_name))
            
            # 更新ignored_contacts列表
            if is_select_all and contact_name not in self.ignored_contacts:
                self.ignored_contacts.append(contact_name)
            elif not is_select_all and contact_name in self.ignored_contacts:
                self.ignored_contacts.remove(contact_name)
            
    def add_ignored_contact(self):
        """添加联系人到联系人列表"""
        contact_name = self.new_contact_entry.get().strip()
        
        if not contact_name:
            messagebox.showwarning("提示", "请输入联系人名称")
            return
            
        # 检查是否已存在
        for item in self.ignored_contacts_tree.get_children():
            values = self.ignored_contacts_tree.item(item, "values")
            if values[1] == contact_name:
                messagebox.showinfo("提示", f"联系人 '{contact_name}' 已在列表中")
                return
        
        # 添加到树形列表
        self.ignored_contacts_tree.insert("", tk.END, values=(
            "√",  # 默认勾选
            contact_name
        ))
        
        # 添加到ignored_contacts列表
        if contact_name not in self.ignored_contacts:
            self.ignored_contacts.append(contact_name)
        
        # 清空输入框
        self.new_contact_entry.delete(0, tk.END)
        
        messagebox.showinfo("成功", f"已添加联系人 '{contact_name}' 到联系人列表")
        
    def delete_ignored_contact(self):
        """从不联系人列表中删除所选联系人"""
        selected_items = self.ignored_contacts_tree.selection()
        
        if not selected_items:
            messagebox.showwarning("提示", "请先选择要删除的联系人")
            return
            
        # 获取选中的联系人
        selected_item = selected_items[0]
        contact_name = self.ignored_contacts_tree.item(selected_item, "values")[1]
        
        # 从树形列表中删除
        self.ignored_contacts_tree.delete(selected_item)
        
        # 从ignored_contacts列表中删除
        if contact_name in self.ignored_contacts:
            self.ignored_contacts.remove(contact_name)
            messagebox.showinfo("成功", f"已从不联系人列表中删除联系人 '{contact_name}'")
        
    def save_ignored_contacts_settings(self):
        """保存联系人设置"""
        # 获取当前所有勾选的联系人
        ignored_contacts = []
        for item in self.ignored_contacts_tree.get_children():
            values = self.ignored_contacts_tree.item(item, "values")
            if values[0] == "√":  # 已勾选
                ignored_contacts.append(values[1])
        
        # 更新ignored_contacts列表
        self.ignored_contacts = ignored_contacts
        
        settings = {
            'ignored_contacts': self.ignored_contacts,
            'contact_filter_mode': self.contact_filter_mode.get()
        }
        
        # 调用回调函数保存设置
        if self.callbacks.get('save_settings'):
            self.callbacks['save_settings'](settings)
            messagebox.showinfo("成功", "联系人设置已保存")
        else:
            print("警告: 没有保存设置的回调函数")
            messagebox.showwarning("警告", "无法保存设置，缺少回调函数")
    
    def get_ignored_contacts_settings(self):
        """获取联系人设置"""
        return {
            'ignored_contacts': self.ignored_contacts,
            'contact_filter_mode': self.contact_filter_mode.get()
        }

    def create_active_dialogue_settings(self, parent):
        """创建主动对话选项卡内容"""
        # 创建主动对话UI
        if not self.active_dialogue_ui06242:
            self.active_dialogue_ui06242 = ActiveDialogueUI06242(parent, self.callbacks)
        else:
            # 如果UI已经存在，更新父窗口
            if hasattr(self.active_dialogue_ui06242, 'main_frame06242'):
                self.active_dialogue_ui06242.main_frame06242.destroy()
            self.active_dialogue_ui06242 = ActiveDialogueUI06242(parent, self.callbacks)

    def create_fixed_reply_settings(self, parent):
        """创建固定回复选项卡内容"""
        # 创建固定回复UI
        if not self.fixed_reply_ui:
            self.fixed_reply_ui = FixedReplyUI(parent, self.callbacks)
        else:
            # 如果UI已经存在，重新创建
            self.fixed_reply_ui = FixedReplyUI(parent, self.callbacks)

    def check_contact_should_be_ignored(self, contact_name):
        """
        检查是否应该忽略联系人的消息
        :param contact_name: 联系人名称
        :return: 如果应该忽略联系人消息则返回True，否则返回False
        """
        # 获取当前筛选模式
        filter_mode = self.contact_filter_mode.get()
        
        # 检查联系人是否匹配列表中的任何一项
        match_found = False
        for ignored_contact in self.ignored_contacts:
            if ignored_contact in contact_name:
                match_found = True
                break
                
        # 根据模式返回结果
        if filter_mode == "ignore":
            # 不理睬模式: 匹配则忽略
            return match_found
        else:  # reply_only模式
            # 仅回复模式: 不匹配则忽略
            return not match_found 

    def update_ignored_contacts_list(self):
        """更新联系人树形列表显示"""
        # 直接使用已保存的忽略联系人列表，不尝试获取树形列表中的现有项目
        if self.ignored_contacts:
            # 如果有忽略的联系人，直接将它们添加到树形列表中
            self.update_ignored_contacts_tree(self.ignored_contacts)
            print(f"已加载 {len(self.ignored_contacts)} 个联系人到列表中")
        else:
            # 清空树形列表
            for item in self.ignored_contacts_tree.get_children():
                self.ignored_contacts_tree.delete(item)
            print("没有联系人可显示") 
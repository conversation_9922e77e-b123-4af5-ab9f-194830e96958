import cv2
import numpy as np


def find_red_dot_with_nms(main_image_path, threshold=0.8, nms_overlap_thresh=0.3):
    """
    在主图像中查找红点（使用模板匹配和非极大值抑制），并在识别到的位置进行标注。

    Args:
        main_image_path (str): 主图像的路径。
        template_image_path (str): 包含红点模板图像的路径。
        threshold (float): 匹配的相似度阈值（0到1之间，越高越严格）。
        nms_overlap_thresh (float): 非极大值抑制的重叠阈值。
                                   如果两个框的IoU（交并比）大于此值，则保留得分最高的。
                                   通常0.2-0.5之间效果较好。

    Returns:
        list: 找到的红点位置列表，每个位置是一个 (x, y) 中心坐标元组。
    """
    # 1. 加载主图像和模板图像
    main_img = cv2.imread(main_image_path)
    template = cv2.imread("voice_red.png")

    if main_img is None:
        print(f"错误: 无法加载主图像 - {main_image_path}")
        return []

    # 获取模板的宽度和高度
    w, h = template.shape[1], template.shape[0]

    # 2. 执行模板匹配
    res = cv2.matchTemplate(main_img, template, cv2.TM_CCOEFF_NORMED)

    # 3. 找到所有匹配度高于阈值的位置
    # np.where 返回匹配点在结果矩阵中的行和列索引
    loc_rows, loc_cols = np.where(res >= threshold)

    # 收集所有的矩形框和对应的分数
    boxes = []
    scores = []
    for r, c in zip(loc_rows, loc_cols):
        # 矩形框的左上角坐标 (x, y) 和宽高 (w, h)
        boxes.append([c, r, w, h])
        # 对应的匹配分数
        scores.append(res[r, c])

    # 转换为 NumPy 数组以便于操作
    boxes = np.array(boxes)
    scores = np.array(scores)

    # 4. 应用非极大值抑制 (NMS)
    # cv2.dnn.NMSBoxes 适用于处理多个检测框，根据分数和重叠度进行筛选
    # 它返回的是保留下来的框的索引
    indices = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(), threshold, nms_overlap_thresh)

    # 如果没有找到任何框，NMSBoxes 可能返回空列表或空数组
    if len(indices) == 0:
        return []

    # NMSBoxes 返回的是一个二维数组，我们需要扁平化它
    indices = indices.flatten()

    red_dot_locations = []
    # 遍历保留下来的索引，获取最终的红点位置
    for i in indices:
        x, y, bw, bh = boxes[i]
        # 记录红点中心坐标
        center_x = x + bw // 2
        center_y = y + bh // 2
        red_dot_locations.append((center_x, center_y))

    return red_dot_locations


def find_voice_logo_with_nms(main_image_path, threshold=0.8, nms_overlap_thresh=0.3):
    # 1. 加载主图像和模板图像
    main_img = cv2.imread(main_image_path)
    template = cv2.imread("voice_logo.png")

    if main_img is None:
        print(f"错误: 无法加载主图像 - {main_image_path}")
        return []

    # 获取模板的宽度和高度
    w, h = template.shape[1], template.shape[0]

    # 2. 执行模板匹配
    res = cv2.matchTemplate(main_img, template, cv2.TM_CCOEFF_NORMED)

    # 3. 找到所有匹配度高于阈值的位置
    # np.where 返回匹配点在结果矩阵中的行和列索引
    loc_rows, loc_cols = np.where(res >= threshold)

    # 收集所有的矩形框和对应的分数
    boxes = []
    scores = []
    for r, c in zip(loc_rows, loc_cols):
        # 矩形框的左上角坐标 (x, y) 和宽高 (w, h)
        boxes.append([c, r, w, h])
        # 对应的匹配分数
        scores.append(res[r, c])

    # 转换为 NumPy 数组以便于操作
    boxes = np.array(boxes)
    scores = np.array(scores)

    # 4. 应用非极大值抑制 (NMS)
    # cv2.dnn.NMSBoxes 适用于处理多个检测框，根据分数和重叠度进行筛选
    # 它返回的是保留下来的框的索引
    indices = cv2.dnn.NMSBoxes(boxes.tolist(), scores.tolist(), threshold, nms_overlap_thresh)

    # 如果没有找到任何框，NMSBoxes 可能返回空列表或空数组
    if len(indices) == 0:
        return []

    # NMSBoxes 返回的是一个二维数组，我们需要扁平化它
    indices = indices.flatten()

    red_dot_locations = []
    # 遍历保留下来的索引，获取最终的红点位置
    for i in indices:
        x, y, bw, bh = boxes[i]
        # 记录红点中心坐标
        center_x = x + bw // 2
        center_y = y + bh // 2
        red_dot_locations.append((center_x, center_y))

    return red_dot_locations
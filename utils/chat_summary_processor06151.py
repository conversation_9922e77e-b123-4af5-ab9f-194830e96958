import os
import json
import time
from datetime import datetime

class ChatSummaryProcessor06151:
    def __init__(self, ai_chat_module=None):
        """
        初始化聊天总结处理器
        :param ai_chat_module: AI聊天模块，用于生成总结
        """
        self.ai_chat_module = ai_chat_module
        self.summary_dir = "summary_chat"
        
        # 确保summary_chat文件夹存在
        if not os.path.exists(self.summary_dir):
            os.makedirs(self.summary_dir)
    
    def set_ai_chat_module(self, ai_chat_module):
        """设置AI聊天模块"""
        self.ai_chat_module = ai_chat_module
    
    def generate_summary(self, contact_name, conversation, prompt, callback=None):
        """
        生成联系人聊天总结
        :param contact_name: 联系人名称
        :param conversation: 对话历史
        :param prompt: 总结提示词模板
        :param callback: 回调函数，用于通知总结完成
        :return: 生成的总结内容字典
        """
        
        if not self.ai_chat_module:
            error_msg = "错误: AI聊天模块未设置，无法生成聊天总结"
            print(error_msg)
            if callback:
                callback({"error": error_msg, "contact_name": contact_name})
            return None
        
        # 加载之前的总结记录
        summary_file = os.path.join(self.summary_dir, f"{contact_name}.json")
        last_summary_time = 0
        last_summary = ""
        summaries = []
        
        if os.path.exists(summary_file):
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summaries = json.load(f)
                    if summaries and isinstance(summaries, list) and len(summaries) > 0:
                        last_summary_time = summaries[-1].get("last_time_summary", 0)
                        last_summary = summaries[-1].get("summary_content", "")
            except Exception as e:
                error_msg = f"读取总结文件出错: {str(e)}"
                print(error_msg)
                if callback:
                    callback({"error": error_msg, "contact_name": contact_name})
                return None
        
        # 提取未总结的消息
        unsummarized_messages = []
        for msg in conversation:
            if msg.get("timestamp", 0) > last_summary_time:
                unsummarized_messages.append(msg)
        
        if not unsummarized_messages:
            error_msg = f"联系人 {contact_name} 没有需要总结的新消息"
            print(error_msg)
            if callback:
                callback({"error": error_msg, "contact_name": contact_name})
            return None
        
        # 处理消息内容为可读文本
        chat_content = ""
        for msg in unsummarized_messages:
            role = "用户" if msg.get("role") == "user" else "助手"
            timestamp = msg.get("timestamp", 0)
            time_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp else ""
            content = msg.get("content", "")
            
            chat_content += f"[{time_str}] {role}: {content}\n\n"
        
        # 准备提示词
        final_prompt = prompt.replace("{chat_content}", chat_content)
        final_prompt = final_prompt.replace("{last_summary}", last_summary)
        
        # 调用AI生成总结
        try:
            summary_content = self.ai_chat_module.generate_text(final_prompt)
            
            if not summary_content:
                error_msg = "生成总结失败: AI返回空内容"
                print(error_msg)
                if callback:
                    callback({"error": error_msg, "contact_name": contact_name})
                return None
            
            # 创建总结记录
            current_time = int(time.time())
            summary_item = {
                "contact_name": contact_name,
                "last_time_summary": current_time,
                "summary_content": summary_content
            }
            
            # 添加到总结列表并保存
            summaries.append(summary_item)
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summaries, f, ensure_ascii=False, indent=2)
            
            # 调用回调函数
            if callback:
                callback(summary_item)
            
            return summary_item
            
        except Exception as e:
            error_msg = f"生成总结时出错: {str(e)}"
            print(error_msg)
            if callback:
                callback({"error": error_msg, "contact_name": contact_name})
            return None
    
    def get_contact_summary(self, contact_name):
        """
        获取指定联系人的所有总结记录
        :param contact_name: 联系人名称
        :return: 总结记录列表
        """
        summary_file = os.path.join(self.summary_dir, f"{contact_name}.json")
        
        if not os.path.exists(summary_file):
            return []
        
        try:
            with open(summary_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取总结文件出错: {str(e)}")
            return []
    
    def get_last_summary(self, contact_name):
        """
        获取指定联系人的最新总结
        :param contact_name: 联系人名称
        :return: 最新总结记录，如果没有则返回None
        """
        summaries = self.get_contact_summary(contact_name)
        
        if not summaries:
            return None
        
        return summaries[-1]
    
    def get_unsummarized_count(self, contact_name, conversation):
        """
        获取指定联系人的未总结消息数量
        :param contact_name: 联系人名称
        :param conversation: 对话历史
        :return: 未总结消息数量
        """
        last_summary = self.get_last_summary(contact_name)
        last_summary_time = last_summary.get("last_time_summary", 0) if last_summary else 0
        
        count = 0
        for msg in conversation:
            if msg.get("timestamp", 0) > last_summary_time:
                count += 1
        
        return count 
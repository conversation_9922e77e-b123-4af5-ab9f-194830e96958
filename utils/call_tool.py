from langchain_core.tools import tool
from utils.mac_send_file import mac_copy_file_to_clipboard_pyobjc
import os
import platform
import pyautogui
import time
import random
import json
from pynput.mouse import <PERSON><PERSON>, Controller
from pynput.keyboard import Key, Controller as KeyboardController
def create_tools():
    @tool
    def send_emojis( emojis_path: str) -> str:
        """发送表情
        Args:
            file_path: 要发送的表情路径
        Returns:
            str: 操作结果
        """

        with open('settings.json', 'r',encoding='utf-8') as f:
            setting = json.load(f)

        emojis_lottery_rate = setting["emojis_lottery_rate"]
        enable_emojis_lottery = setting["enable_emojis_lottery"]

        if not enable_emojis_lottery:
            return "用户未开启表情发送，跳过，本次请忽略发送表情的指令"
        
        if random.random() > float(emojis_lottery_rate):
            return "本次随机结果：不发送表情,本次请忽略发送表情的指令"

        try:
            # 拼接从settings.json获取的表情文件夹路径
            if not os.path.exists(emojis_path):
                with open('settings.json', 'r',encoding='utf-8') as f:
                    setting = json.load(f)
                    emojis_folder_path = setting["emojis_folder_path"]
                    emojis_path = os.path.join(emojis_folder_path,emojis_path)
            if os.path.exists(emojis_path):
                if platform.system() == 'Darwin':  # macOS
                    mac_copy_file_to_clipboard_pyobjc(emojis_path)
                    time.sleep(0.5)
                    pyautogui.hotkey('command', 'v')
                    time.sleep(0.5)
                    pyautogui.press('enter')
                else:  # Windows/Linux
                    setClipboardFile(emojis_path)
                    time.sleep(0.5)
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.5)
                    # 输入回车发送
                    mouse = Controller()
                    keyboard = KeyboardController()
                    keyboard.press(Key.enter)
                    keyboard.release(Key.enter)
                return f"文件 {emojis_path} 已发送成功"
            else:
                return f"文件 {emojis_path} 不存在"
        except Exception as e:
            return f"发送完毕,但发生错误"
        
    @tool
    def send_file(file_path: str) -> str:
        """发送文件
        Args:
            file_path: 要发送的文件路径
        Returns:
            str: 操作结果
        """
        try:
            # 拼接从settings.json获取的表情文件夹路径
            if not os.path.exists(file_path):
                with open('settings.json', 'r',encoding='utf-8') as f:
                    setting = json.load(f)
                    files_folder_path = setting["files_folder_path"]
                    file_path = os.path.join(files_folder_path,file_path)
            if os.path.exists(file_path):
                if platform.system() == 'Darwin':  # macOS
                    mac_copy_file_to_clipboard_pyobjc(file_path)
                    time.sleep(0.5)
                    pyautogui.hotkey('command', 'v')
                    time.sleep(0.5)
                    pyautogui.press('enter')
                    return f"文件 {file_path} 已发送成功"
                else:  # Windows/Linux
                    setClipboardFile(file_path)
                    time.sleep(0.5)
                    pyautogui.hotkey('ctrl', 'v')
                    time.sleep(0.5)
                    # 输入回车发送
                    mouse = Controller()
                    keyboard = KeyboardController()
                    keyboard.press(Key.enter)
                    keyboard.release(Key.enter)
                    return f"文件 {file_path} 已发送成功"
            else:
                return f"文件 {file_path} 不存在"
        except Exception as e:
            return f"发送完毕,但发生错误"
        
    return [send_emojis, send_file]
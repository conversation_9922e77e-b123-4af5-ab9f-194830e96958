import os
import json
import time

class StorageManager:
    def __init__(self, storage_dir="chat_history", settings_file="settings.json"):
        """
        初始化存储管理器
        :param storage_dir: 聊天历史存储目录
        :param settings_file: 设置文件路径
        """
        self.storage_dir = storage_dir
        self.settings_file = settings_file
        
        # 确保存储目录存在
        if not os.path.exists(self.storage_dir):
            os.makedirs(self.storage_dir)
            print(f"创建聊天历史存储目录: {self.storage_dir}")
        else:
            print(f"使用现有聊天历史存储目录: {self.storage_dir}")
            
    def load_settings(self):
        """
        加载设置
        :return: 设置字典
        """
        default_settings = {
            'api_key': '',
            'api_base_url': 'https://api.openai.com',
            'model_name': 'gpt-3.5-turbo',
            'system_prompt': '你是一个有用的助手。请用简短、友好的方式回答问题。',
            'default_system_prompt': '你是一个有用的助手。请用简短、友好的方式回答问题。',
            'prompt_templates': [],
            'mask_green_bubbles': True,
            'green_r': 80,
            'green_g': 200,
            'green_b': 80,
            'green_tol': 40,
            'green_min_area': 100,
            'daily_summary_time': '18:00',
            'mail_host': '',
            'mail_user': '',
            'mail_pass': '',
            'receivers': '',
            'voice_bubble_offset': 100,
            'ignored_contacts': [],
            'fixed_reply_rules': []
        }
        
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    # 合并默认设置和已保存设置
                    for key, value in default_settings.items():
                        if key not in settings:
                            settings[key] = value
                    
                    # 兼容处理：如果没有default_system_prompt，使用system_prompt值
                    if 'default_system_prompt' not in settings:
                        settings['default_system_prompt'] = settings.get('system_prompt', default_settings['system_prompt'])
                    
                    return settings
        except Exception as e:
            print(f"加载设置出错: {str(e)}")
            
        return default_settings
    
    def save_settings(self, settings):
        """
        保存设置
        :param settings: 设置字典
        :return: 成功返回True，失败返回False
        """
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存设置出错: {str(e)}")
            return False
            
    def load_conversation(self, contact_name):
        """
        加载特定联系人的对话历史
        :param contact_name: 联系人名称
        :return: 对话历史列表
        """
        try:
            clear_name = contact_name.replace('.', '_').replace('/', '_').replace('\\', '_')
            file_path = os.path.join(self.storage_dir, f"{clear_name}.json")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    conversation_data = json.load(f)
                    
                    # 确保所有消息都有is_replied字段
                    for msg in conversation_data:
                        # 确保用户消息后面的助手消息将用户消息标记为已回复
                        if msg["role"] == "user" and not msg.get("is_replied", False):
                            # 检查这条用户消息后面是否有助手回复
                            idx = conversation_data.index(msg)
                            if idx + 1 < len(conversation_data) and conversation_data[idx + 1]["role"] == "assistant":
                                msg["is_replied"] = True
                    
                    return conversation_data
            return []
        except Exception as e:
            print(f"加载'{contact_name}'的聊天历史时出错: {str(e)}")
            return []
            
    def save_conversation(self, contact_name, conversation_data):
        """
        保存特定联系人的聊天历史
        :param contact_name: 联系人名称
        :param conversation_data: 对话历史列表
        :return: 成功返回True，失败返回False
        """
        if not contact_name:
            print("未提供联系人名称，无法保存聊天历史")
            return False
            
        try:
            # 保存前检查所有消息是否有正确的is_replied标记
            for i, msg in enumerate(conversation_data):
                if msg["role"] == "user" and not msg.get("is_replied", False):
                    # 检查这条用户消息后面是否有助手回复，如果有则标记为已回复
                    if i + 1 < len(conversation_data) and conversation_data[i + 1]["role"] == "assistant":
                        msg["is_replied"] = True
            clear_name = contact_name.replace('.', '_').replace('/', '_').replace('\\', '_')
            file_path = os.path.join(self.storage_dir, f"{clear_name}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存'{contact_name}'的聊天历史时出错: {str(e)}")
            return False
            
    def load_all_conversations(self):
        """
        加载所有已保存的聊天历史
        :return: {联系人名称: 对话历史列表} 格式的字典
        """
        conversations = {}
        contact_message_counts = {}
        
        try:
            # 遍历聊天历史目录中的所有文件
            for filename in os.listdir(self.storage_dir):
                if filename.endswith(".json"):
                    contact_name = os.path.splitext(filename)[0]
                    conversation_data = self.load_conversation(contact_name)
                    
                    # 存储会话历史
                    conversations[contact_name] = conversation_data
                    
                    # 更新消息计数
                    contact_message_counts[contact_name] = len(conversation_data)
        except Exception as e:
            print(f"加载聊天历史时出错: {str(e)}")
            
        return conversations, contact_message_counts
        
    def load_areas(self):
        """
        从settings.json加载区域设置
        :return: 区域设置字典
        """
        try:
            if os.path.exists(self.settings_file):
                print(f"正在从 {self.settings_file} 加载区域设置...")
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                    # 提取所有区域相关的设置
                    areas = {
                        'chat_area': settings.get('chat_area'),
                        'input_area': settings.get('input_area'),
                        'contact_area': settings.get('contact_area'),
                        'contact_name_area': settings.get('contact_name_area'),
                        'unique_id_area': settings.get('unique_id_area'),
                        'unread_bubble_area': settings.get('unread_bubble_area'),
                        'search_area': settings.get('search_area'),
                        'noise_areas': settings.get('noise_areas', [])
                    }
                    
                    # 调试输出每个区域的值
                    for key, value in areas.items():
                        print(f"已加载区域: {key} = {value}")
                    
                    print(f"从settings.json加载区域设置完成")
                    return areas
        except Exception as e:
            print(f"加载区域设置出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
        # 如果加载失败，返回空区域设置
        return {
            'chat_area': None,
            'input_area': None,
            'contact_area': None,
            'contact_name_area': None,
            'unique_id_area': None,
            'unread_bubble_area': None,
            'search_area': None,
            'noise_areas': []
        } 
import json
import sys
import tkinter as tk
import queue
import time
import os
import threading
import cv2
import numpy as np
from PIL import Image, ImageTk
import markdown
import platform
import re
from tkinter import ttk, messagebox
from pynput.mouse import <PERSON><PERSON>, Controller
from utils.voice_read_detector import find_red_dot_with_nms
import pyautogui

# 导入自定义模块
from gui.ui_main import MainUI
from gui.settings_ui import SettingsUI
from gui.selection_ui import SelectionUI
from gui.history_ui import HistoryUI
from monitor.area_monitor import AreaMonitor
from ocr.message_extractor import MessageExtractor
from utils.storage import StorageManager
from utils.ai_chat import AiChat
from utils.image_processor import capture_screen_area
from utils.system_tools import check_lisence
from utils.chat_summary_processor06151 import ChatSummaryProcessor06151
from utils.voice_read_detector import find_red_dot_with_nms
from utils.mouse_takeover_detector06191 import MouseTakeoverDetector06191  # 导入鼠标接管检测器

class ScreenChatBot:
    def __init__(self, root):
        """
        应用程序主控制器
        :param root: Tkinter根窗口
        """
        self.root = root
        
        # 创建UI回调函数字典
        self.callbacks = {
            'select_areas': self.select_areas,
            'start_monitoring': self.start_monitoring,
            'stop_monitoring': self.stop_monitoring,
            'toggle_debug': self.toggle_debug,
            'clear_log': self.clear_log,
            'test_reply': self.test_reply,
            'save_panel_settings': self.save_panel_settings,
            'toggle_green_mask': self.toggle_green_mask,
            'toggle_text_color': self.toggle_text_color,
            'test_green_mask': self.test_green_mask,
            'test_text_color': self.test_text_color,
            'show_conversation_history': self.show_conversation_history,
            'save_api_settings': self.save_api_settings,
            'reset_areas': self.reset_areas,
            'get_settings': self.get_settings,
            'save_prompt_templates': self.save_prompt_templates,
            'save_daily_summary_settings': self.save_daily_summary_settings,
            'run_summary_now': self.run_summary_now,
            'save_chat_summary_settings': self.save_chat_summary_settings,
            'generate_chat_summary': self.generate_chat_summary,
            'save_settings': self.save_settings,
            'test_voice06181': self.test_voice06181  # 添加测试语音功能回调
        }
        
        # 初始化UI
        self.ui = MainUI(root, self.callbacks)
        self.settings_ui = None
        
        # 初始化状态变量
        self.selection_done = False
        self.monitoring = False
        self.is_replying = False
        self.debug_mode = True
        self.reply_start_time = 0
        self.reply_timeout = 60
        self.contact_switch_triggered = False  # 联系人切换触发标志
        self.contact_switching = False         # 正在切换联系人标志
        self.stop_requested06141 = False  # 确保正确初始化为False
        
        # 聊天总结模块 (新增)
        self.chat_summary_processor06151 = ChatSummaryProcessor06151()
        
        # 鼠标接管检测器 (新增)
        self.mouse_takeover_detector06191 = MouseTakeoverDetector06191(self)
        
        # 区域坐标
        self.chat_area = None
        self.input_area = None
        self.contact_area = None
        self.contact_name_area = None  # 添加联系人姓名区域
        self.noise_areas = []
        self.screen_center_x = 0
        
        # 初始化组件
        self.message_extractor = MessageExtractor()
        self.storage_manager = StorageManager()
        self.area_monitor = AreaMonitor(self)
        
        # 加载设置
        self.settings = self.storage_manager.load_settings()
        self.debug_mode = True
        
        # 验证每日总结提示词是否正确加载
        if 'daily_summary_prompt' in self.settings:
            print(f"已从settings.json加载每日总结提示词: {self.settings['daily_summary_prompt'][:50]}...")
        else:
            print("警告: 未在settings.json中找到每日总结提示词")
        
        # 初始化日志队列
        self.log_queue = queue.Queue()
        
        # 多人会话管理
        self.current_contact = None  # 当前联系人昵称
        self.conversations = {}  # 存储多个会话的字典 {昵称: 对话历史列表}
        self.contact_message_counts = {}  # 存储每个联系人的消息数量
        self.conversation_history = []  # 当前对话历史
        self.last_messages = []  # 上一次识别到的消息
        self.last_message_count = 0
        
        # 每日总结定时器
        self.summary_scheduled = False
        
        # 加载所有对话历史
        self.load_conversations()
        
    def setup_ui(self):
        """设置UI组件"""
        # 先创建设置界面
        self.settings_ui = SettingsUI(self.root, self.callbacks)
        
        # 为设置选项卡提供回调
        def settings_tab_callback(parent):
            # 添加调试日志
            print("=== settings_tab_callback 被调用 ===")
            print(f"parent类型: {type(parent)}")
            
            # 创建设置选项卡内容
            settings_frame = self.settings_ui.create_settings_tab(parent)
            
            # 确保区域设置显示正确
            areas = {
                'chat_area': self.chat_area,
                'input_area': self.input_area,
                'contact_area': self.contact_area,
                'noise_areas': self.noise_areas
            }
            self.settings_ui.update_area_tree(areas)
            
            # 在UI组件创建完成后，再设置API参数
            self.settings_ui.set_api_settings(self.settings)
            
            print("=== settings_tab_callback 执行完毕 ===")
            return settings_frame
            
        self.callbacks['create_settings_tab'] = settings_tab_callback
        
        # 添加获取设置的回调函数
        self.callbacks['get_settings'] = self.get_settings
        
        # 添加保存提示词模板的回调函数
        self.callbacks['save_prompt_templates'] = self.save_prompt_templates
        
        # 添加保存控制面板设置的回调函数
        self.callbacks['save_panel_settings'] = self.save_panel_settings
        
        # 重要：在所有回调设置完成后，再创建主界面组件
        self.ui.create_widgets()
        
        # 添加选项卡切换事件处理
        def on_tab_change(event):
            self.debug_log(f"选项卡切换: {event.widget.index('current')}")
            # 如果切换到设置选项卡，确保设置界面已正确加载
            if event.widget.index("current") == 1:  # 设置选项卡索引为1
                self.debug_log("切换到设置选项卡")
                # 更新基本设置中的区域树
                self.update_settings_area_tree()
                
                # 更新设置UI中的内容
                if self.settings_ui:
                    # 更新区域树
                    areas = {
                        'chat_area': self.chat_area,
                        'input_area': self.input_area,
                        'contact_area': self.contact_area,
                        'noise_areas': self.noise_areas
                    }
                    self.settings_ui.update_area_tree(areas)
        
        # 绑定选项卡切换事件
        self.ui.tab_control.bind("<<NotebookTabChanged>>", on_tab_change)
        
        # 启动日志处理
        self.root.after(100, self.process_log_queue)
        
        # 初始化日志信息
        self.log("程序已启动，请使用<选择区域>按钮开始...")
        
        # 启动每日总结定时任务
        self.schedule_daily_summary()
        
        # 从设置中加载控制面板参数
        if hasattr(self.ui, 'set_settings'):
            self.ui.set_settings(self.settings)
            self.log("已从settings.json加载控制面板设置")

    def save_panel_settings(self):
        """保存控制面板设置"""
        # 获取当前控制面板设置
        panel_settings = self.ui.get_settings()
        
        # 更新设置字典
        self.settings.update(panel_settings)
        
        # 保存到文件
        if self.storage_manager.save_settings(self.settings):
            self.log("控制面板设置已保存到settings.json")
        else:
            self.log("保存控制面板设置失败")
            
        # 调试信息
        self.debug_log(f"已保存的控制面板设置: {list(panel_settings.keys())[:5]}...")
        
        return True
    
    def process_log_queue(self):
        """处理日志队列，减少UI阻塞"""
        try:
            while not self.log_queue.empty():
                log_entry = self.log_queue.get_nowait()
                if hasattr(self.ui, 'add_log'):
                    self.ui.add_log(log_entry)
        except Exception:
            pass
        finally:
            # 安排下一次处理
            self.root.after(100, self.process_log_queue)
    
    def log(self, message):
        """添加日志到日志框"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 将日志放入队列，而不是直接更新UI
        self.log_queue.put(log_entry)
        
        # 也打印到控制台
        print(log_entry)
    
    def debug_log(self, message):
        """只在调试模式下记录的日志"""
        if self.debug_mode:
            self.log(f"[DEBUG] {message}")
    
    def toggle_debug(self):
        """切换调试模式状态"""
        self.debug_mode = self.ui.debug_var.get()
        self.log(f"调试模式已{'开启' if self.debug_mode else '关闭'}")
    
    def clear_log(self):
        """清除日志内容"""
        self.ui.clear_log()
    
    def select_areas(self):
        """选择屏幕区域"""
        self.log("请在同一画布上框选四个区域: 聊天区、输入区、联系人区、联系人姓名区")
        self.log("操作说明: 先画第一个区域(聊天区)，确认后按1键; 再画第二个区域(输入区)，确认后按2键;")
        self.log("然后画第三个区域(联系人区)，确认后按3键; 最后画第四个区域(联系人姓名区)，确认后按4键")
        self.log("您还可以框选多个干扰区域，每框选一个按5键确认，这些区域将被白色遮罩覆盖")
        self.log("完成所有区域选择后按Enter键确认，或按Esc键取消")
        
        def selection_callback(result):
            """选择区域完成后的回调"""
            self.root.deiconify()
            
            # 保存区域坐标
            self.chat_area = result.get('chat_area')
            self.input_area = result.get('input_area')
            self.contact_area = result.get('contact_area')
            self.contact_name_area = result.get('contact_name_area')
            self.noise_areas = result.get('noise_areas', [])
            
            # 设置中心坐标
            if self.chat_area:
                x1, y1, x2, y2 = self.chat_area
                self.screen_center_x = (x1 + x2) / 2
            
            self.selection_done = True
            
            self.log(f"已完成所有区域选择:")
            self.log(f"- 聊天区: {self.chat_area}")
            self.log(f"- 输入区: {self.input_area}")
            self.log(f"- 联系人区: {self.contact_area}")
            if self.contact_name_area:
                self.log(f"- 联系人姓名区: {self.contact_name_area}")
            if self.noise_areas:
                self.log(f"- 干扰区: {len(self.noise_areas)}个")
                for i, noise_area in enumerate(self.noise_areas):
                    self.log(f"  干扰区{i+1}: {noise_area}")
            
            # 更新UI状态
            self.ui.enable_start_button(True)
            self.ui.enable_test_reply_button(True)
            
            # 更新预览
            self.update_preview()
            
            # 直接更新基本设置界面中的区域树
            self.update_settings_area_tree()
            
            # 也更新设置UI中的区域树（如果存在）
            if self.settings_ui:
                areas = {
                    'chat_area': self.chat_area,
                    'input_area': self.input_area,
                    'contact_area': self.contact_area,
                    'contact_name_area': self.contact_name_area,
                    'noise_areas': self.noise_areas
                }
                self.settings_ui.update_area_tree(areas)
            
            # 立即进行一次联系人名称识别并初始化全局变量
            self.log("正在初始化联系人名称...")
            try:
                # 检查联系人名称区域是否已选择
                if self.contact_name_area:
                    x1, y1, x2, y2 = self.contact_name_area
                    # 截取联系人名称区域
                    from utils.image_processor import capture_screen_area
                    screenshot = capture_screen_area(x1, y1, x2, y2, [])
                    
                    # 构建OCR参数
                    ocr_params = {
                        'debug_mode': self.debug_mode,
                    }
                    
                    # 进行OCR识别
                    contact_name = self.message_extractor.extract_contact_name(screenshot, ocr_params)
                    
                    # 更新联系人名称全局变量
                    if contact_name:
                        self.log(f"初始化联系人名称: {contact_name}")
                        self.switch_contact(contact_name)
                        # 设置联系人切换标志为False，表示已经初始化
                        self.contact_switch_triggered = False
                    else:
                        self.log("无法识别联系人名称，将使用'未知联系人'")
                        self.switch_contact("未知联系人")
                else:
                    self.log("联系人名称区域未选择，暂不初始化联系人名称")
            except Exception as e:
                self.log(f"初始化联系人名称时出错: {str(e)}")
                import traceback
                self.debug_log(traceback.format_exc())
        
        # 创建并启动选择界面
        selection_ui = SelectionUI(selection_callback)
        selection_ui.start_selection(self.root)
    
    def update_settings_area_tree(self):
        """更新设置选项卡中的区域树"""
        # 如果UI中有区域树，直接更新
        if hasattr(self.ui, 'settings_area_tree'):
            try:
                # 清除现有条目
                self.ui.settings_area_tree.delete(*self.ui.settings_area_tree.get_children())
                
                # 添加基本区域
                areas = [
                    ("聊天区", str(self.chat_area) if self.chat_area else "[未选择]"),
                    ("输入区", str(self.input_area) if self.input_area else "[未选择]"),
                    ("联系人区", str(self.contact_area) if self.contact_area else "[未选择]"),
                    ("联系人姓名区", str(self.contact_name_area) if self.contact_name_area else "[未选择]")
                ]
                for area in areas:
                    self.ui.settings_area_tree.insert("", tk.END, values=area)
                
                # 添加所有干扰区
                if self.noise_areas:
                    for i, noise_area in enumerate(self.noise_areas):
                        self.ui.settings_area_tree.insert("", tk.END, values=(f"干扰区{i+1}", str(noise_area)))
                        
                self.debug_log("已更新设置界面中的区域树")
            except Exception as e:
                self.debug_log(f"更新区域树出错: {str(e)}")
                
    def reset_areas(self):
        """重置所有区域"""
        self.chat_area = None
        self.input_area = None
        self.contact_area = None
        self.contact_name_area = None
        self.noise_areas = []
        self.selection_done = False
        
        # 更新UI状态
        self.ui.enable_start_button(False)
        self.ui.enable_stop_button(False)
        self.ui.enable_test_reply_button(False)
        
        # 更新设置页面中的区域信息
        if self.settings_ui:
            areas = {
                'chat_area': None,
                'input_area': None,
                'contact_area': None,
                'contact_name_area': None,
                'noise_areas': []
            }
            self.settings_ui.update_area_tree(areas)
            
        self.log("所有区域已重置，请重新选择区域")
    
    def start_monitoring(self):
        """开始监控所选区域"""
        # 检查是否已经选择了区域
        if not self.selection_done:
            self.log("请先选择监控区域")
            return
        
        # 重置停止请求标志
        self.stop_requested06141 = False
        
        if not self.area_monitor.start_monitoring():
            self.log("监控启动失败")
            return
            
        # 启动鼠标接管检测器
        self.mouse_takeover_detector06191.start_monitoring06191()
            
        self.monitoring = True
        self.ui.update_monitoring_status(True)
        self.log("已开始监控")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
            
        if not self.area_monitor.stop_monitoring():
            self.log("监控停止失败")
            return
            
        # 停止鼠标接管检测器
        self.mouse_takeover_detector06191.stop_monitoring06191()
        
        # 如果正在生成回复或键入字符，则停止
        if self.is_replying:
            self.is_replying = False
            self.log("停止监控：已中断正在进行的回复生成")
            
        # 添加一个标志来表示用户已请求停止
        self.stop_requested06141 = True
            
        self.monitoring = False
        self.ui.update_monitoring_status(False)
        self.log("已停止监控")
    
    def test_reply(self):
        """测试回复功能"""
        if self.selection_done:
            if not self.current_contact:
                tk.messagebox.showinfo("提示", "请先进行聊天监控，等待系统识别联系人后再测试回复")
                return
                
            # 如果正在回复，不允许再次发起回复
            if self.is_replying:
                self.log("正在回复中，请等待当前回复完成")
                return
                
            test_message = f"这是发给{self.current_contact}的测试消息"
            self.log(f"测试回复功能，联系人: {self.current_contact}, 模拟消息: {test_message}")
            
            # 设置回复标志
            self.is_replying = True
            self.reply_start_time = time.time()
            
            # 重置停止请求标志
            self.stop_requested06141 = False
            
            # 将测试消息添加到对话历史（作为用户消息），并标记为未回复
            test_msg = {
                "role": "user", 
                "content": test_message, 
                "is_replied": False,
                "timestamp": int(time.time())  # 添加时间戳
            }
            self.conversation_history.append(test_msg)
            self.log(f"已添加测试用户消息到对话历史")
            
            # 更新当前联系人的会话历史
            self.conversations[self.current_contact] = self.conversation_history
            
            # 获取针对当前联系人的系统提示词
            system_prompt = self.get_system_prompt_for_contact(self.current_contact)
            # 确保system_prompt不为None，如果是None则使用空字符串
            if system_prompt is None:
                system_prompt = ""
                self.debug_log("警告：获取到的system_prompt为None，已设置为空字符串")
            
            self.debug_log(f"联系人'{self.current_contact}'使用系统提示词: {system_prompt[:50]}..." if system_prompt and len(system_prompt) > 50 else system_prompt)
            
            # 创建AI聊天实例
            api_settings = {
                'provider': self.settings.get('provider', 'openai'),
                'api_key': self.settings.get('api_key', ''),
                'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
                'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
                'bot_id': self.settings.get('bot_id', ''),
                'token': self.settings.get('token', ''),
                'system_prompt': system_prompt,
                'delay': self.ui.delay_var.get(),
                'debug_mode': self.debug_mode
            }
            self.debug_log(f"测试回复使用API提供商: {api_settings['provider']}")
            ai_chat = AiChat(api_settings, stop_flag=lambda: self.stop_requested06141)
            
            # 获取过滤后的会话历史（只包含最后总结之后的消息）
            filtered_history = self.get_filtered_conversation_history(self.current_contact)
            
            # 生成回复
            reply_list = ai_chat.generate_reply(filtered_history, self.input_area)
            reply = reply_list[-1]["content"]
            # 模拟输入回复
            ai_chat.type_reply(reply, self.input_area)
            

            self.conversation_history += reply_list
            self.log(f"【助手回复】已添加到对话历史: {reply[:50]}..." if len(reply) > 50 else reply)
            
            # 标记测试消息为已回复
            test_msg["is_replied"] = True
            self.log("测试消息已标记为已回复")
            
            # 更新并保存会话历史
            self.conversations[self.current_contact] = self.conversation_history
            self.storage_manager.save_conversation(self.current_contact, self.conversation_history)
            
            # 清除回复标志
            self.is_replying = False
            self.log("测试回复完成")
        else:
            self.log("请先选择区域后再测试回复功能")
    
    def update_preview(self):
        """更新预览区域"""
        if self.selection_done and self.chat_area:
            try:
                # 截取屏幕区域
                x1, y1, x2, y2 = self.chat_area
                screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
                
                # 更新UI预览
                self.ui.update_preview(screenshot)
                
                return True
            except Exception as e:
                self.log(f"更新预览失败: {str(e)}")
                return False
        return False
    
    def toggle_green_mask(self):
        """切换是否启用绿色气泡遮罩"""
        mask_green = self.ui.mask_green_var.get()
        self.log(f"{'启用' if mask_green else '禁用'}绿色气泡遮罩")
    
    def toggle_text_color(self):
        """切换是否启用文字颜色过滤"""
        text_color_enabled = self.ui.text_color_enabled_var.get()
        self.log(f"{'启用' if text_color_enabled else '禁用'}文字颜色过滤")
        if text_color_enabled:
            r = self.ui.text_color_r_var.get()
            g = self.ui.text_color_g_var.get()
            b = self.ui.text_color_b_var.get()
            tol = self.ui.text_color_tol_var.get()
            self.log(f"文字颜色设置: R({r}), G({g}), B({b}), 容差({tol})")
    
    def test_green_mask(self):
        """测试绿色气泡遮罩功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试绿色气泡遮罩功能")
            return
            
        self.log("测试绿色气泡遮罩功能...")
        try:
            # 更新绿色气泡参数
            green_r = self.ui.green_r_var.get()
            green_g = self.ui.green_g_var.get()
            green_b = self.ui.green_b_var.get()
            green_tol = self.ui.green_tol_var.get()
            green_min_area = self.ui.green_min_area_var.get()
            
            self.log(f"当前绿色气泡参数: R({green_r}), G({green_g}), B({green_b}), 容差({green_tol}), 最小面积({green_min_area})")
            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存原始截图
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"test_original_{timestamp}.png")
            cv2.imwrite(original_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"原始截图已保存: {original_path}")
            
            # 应用绿色气泡遮罩
            from utils.image_processor import preprocess_image_mask_green_bubbles
            green_params = {
                'green_r': green_r,
                'green_g': green_g,
                'green_b': green_b,
                'green_tol': green_tol,
                'green_min_area': green_min_area,
                'debug_mode': True
            }
            processed_img = preprocess_image_mask_green_bubbles(screenshot, green_params)
            
            # 保存处理后的图像
            processed_path = os.path.join(debug_dir, f"test_masked_{timestamp}.png")
            cv2.imwrite(processed_path, processed_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"处理后图像已保存: {processed_path}")
            
            # 计算处理前后的差异
            diff = cv2.absdiff(screenshot, processed_img)
            diff_sum = np.sum(diff)
            if diff_sum > 0:
                self.log(f"检测到差异，遮罩功能有效。差异值: {diff_sum}")
                
                # 保存差异图像
                diff_path = os.path.join(debug_dir, f"test_diff_{timestamp}.png")
                cv2.imwrite(diff_path, diff, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                self.log(f"差异图像已保存: {diff_path}")
            else:
                self.log("未检测到差异，遮罩功能可能无效。请调整参数后重试。")
                
            self.log("绿色气泡遮罩测试完成，请查看debug_screenshots目录中的测试图像")
            
        except Exception as e:
            self.log(f"测试绿色气泡遮罩时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_text_color(self):
        """测试文字颜色过滤功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试文字颜色过滤功能")
            return
            
        self.log("测试文字颜色过滤功能...")
        try:
            # 获取文字颜色参数
            text_color_r = self.ui.text_color_r_var.get()
            text_color_g = self.ui.text_color_g_var.get()
            text_color_b = self.ui.text_color_b_var.get()
            text_color_tol = self.ui.text_color_tol_var.get()
            
            self.log(f"当前文字颜色参数: R({text_color_r}), G({text_color_g}), B({text_color_b}), 容差({text_color_tol})")
            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存原始截图
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"text_color_test_original_{timestamp}.png")
            cv2.imwrite(original_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"原始截图已保存: {original_path}")
            
            # 应用文字颜色过滤
            from utils.image_processor import filter_text_by_color
            color_params = {
                'text_color_r': text_color_r,
                'text_color_g': text_color_g,
                'text_color_b': text_color_b,
                'text_color_tol': text_color_tol,
                'debug_mode': True
            }
            filtered_img = filter_text_by_color(screenshot, color_params)
            
            # 保存处理后的图像
            filtered_path = os.path.join(debug_dir, f"text_color_test_filtered_{timestamp}.png")
            cv2.imwrite(filtered_path, filtered_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"过滤后图像已保存: {filtered_path}")
            
            # 创建颜色范围掩码用于显示
            # 在OpenCV中是BGR顺序
            lower_color_bgr = np.array([max(0, text_color_b-text_color_tol), max(0, text_color_g-text_color_tol), max(0, text_color_r-text_color_tol)])
            upper_color_bgr = np.array([min(255, text_color_b+text_color_tol), min(255, text_color_g+text_color_tol), min(255, text_color_r+text_color_tol)])
            
            # 创建BGR掩码
            color_mask = cv2.inRange(screenshot, lower_color_bgr, upper_color_bgr)
            
            # 保存掩码图像
            mask_path = os.path.join(debug_dir, f"text_color_test_mask_{timestamp}.png")
            cv2.imwrite(mask_path, color_mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"颜色掩码已保存: {mask_path}")
            
            # 显示处理后的图像
            self.ui.update_preview(filtered_img)
            self.log("已在预览区域显示过滤后的图像")
            
            # 更新UI
            self.ui.text_color_enabled_var.set(True)
            
        except Exception as e:
            self.log(f"测试文字颜色过滤出错: {str(e)}")
    
    def test_voice06181(self):
        """测试语音消息红点检测和处理功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试语音功能")
            return
            
        self.log("测试语音消息红点检测功能...")
        try:

            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存截图用于检测
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            debug_img_path = os.path.join(debug_dir, f"voice_test_{timestamp}.png")
            cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"已保存语音测试截图: {debug_img_path}")
            
            # 获取截图尺寸
            img_height06181, img_width06181 = screenshot.shape[:2]
            self.log(f"截图尺寸: 宽={img_width06181}, 高={img_height06181}")
            
            # 获取聊天区域尺寸
            chat_width06181 = x2 - x1
            chat_height06181 = y2 - y1
            self.log(f"聊天区域尺寸: 宽={chat_width06181}, 高={chat_height06181}")
            
            # 计算缩放比例 (如果截图尺寸与聊天区域不一致)
            scale_x06181 = chat_width06181 / img_width06181
            scale_y06181 = chat_height06181 / img_height06181
            self.log(f"缩放比例: x={scale_x06181}, y={scale_y06181}")
            
            # 检测语音红点
            self.log("正在检测语音消息红点...")
            voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
            
            if voice_red_dots06181:
                self.log(f"找到 {len(voice_red_dots06181)} 个语音消息红点")
                
                # 获取第一个红点的坐标
                dot_x06181, dot_y06181 = voice_red_dots06181[0]
                self.log(f"处理语音红点，截图中的相对坐标: ({dot_x06181}, {dot_y06181})")
                
                # 应用缩放比例
                scaled_x06181 = int(dot_x06181 * scale_x06181)
                scaled_y06181 = int(dot_y06181 * scale_y06181)
                self.log(f"缩放后坐标: ({scaled_x06181}, {scaled_y06181})")
                
                # 计算语音气泡位置 (红点左侧约100像素处)
                voice_bubble_x06181 = scaled_x06181 - int(100 * scale_x06181)
                # 限制不要超出屏幕边界
                voice_bubble_x06181 = max(10, voice_bubble_x06181)
                
                # 转换为屏幕绝对坐标
                abs_x06181 = x1 + voice_bubble_x06181
                abs_y06181 = y1 + scaled_y06181
                
                # 记录详细的坐标信息用于调试
                self.log(f"聊天区域在屏幕上的位置: ({x1}, {y1}, {x2}, {y2})")
                self.log(f"点击语音消息，屏幕绝对坐标: ({abs_x06181}, {abs_y06181})")
                
                # 确保坐标在有效范围内
                if x1 <= abs_x06181 <= x2 and y1 <= abs_y06181 <= y2:
                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(self.ui, 'mouse_interval_var'):
                        mouse_interval = self.ui.mouse_interval_var.get()
                    
                    # 移动鼠标并点击
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    mouse.position = (abs_x06181, abs_y06181)
                    mouse.press(Button.left)
                    mouse.press(Button.right)
                    mouse.release(Button.left)
                    mouse.release(Button.right)
                    new_abs_x06181 = abs_x06181 + 15
                    new_abs_y06181 = abs_y06181 + 15
                    mouse.position = (new_abs_x06181, new_abs_y06181)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_abs_x06181, new_abs_y06181-50)

                else:
                    self.debug_log(f"警告：计算出的坐标 ({abs_x06181}, {abs_y06181}) 超出了聊天区域范围，调整点击位置")
                    # 调整到区域内的安全位置
                    safe_x = min(max(abs_x06181, x1 + 10), x2 - 10)
                    safe_y = min(max(abs_y06181, y1 + 10), y2 - 10)
                    self.debug_log(f"调整后的安全坐标: ({safe_x}, {safe_y})")

                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(self.ui, 'mouse_interval_var'):
                        mouse_interval = self.ui.mouse_interval_var.get()
                        
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    mouse.position = (safe_x, safe_y)
                    mouse.press(Button.left)
                    mouse.press(Button.right)
                    mouse.release(Button.left)
                    mouse.release(Button.right)
                    new_safe_x = safe_x + 15
                    new_safe_y = safe_y + 15
                    mouse.position = (new_safe_x, new_safe_y)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_safe_x, new_safe_y-50)

                # 等待语音消息处理完成
                # 获取等待转文字时间
                voice_wait = 6.0  # 默认值
                if hasattr(self.ui, 'voice_wait_var'):
                    voice_wait = self.ui.voice_wait_var.get()
                self.debug_log(f"等待语音转文字完成，等待时间: {voice_wait}秒")
                time.sleep(voice_wait)
                
                # 再次截图检测红点
                screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_img_path = os.path.join(debug_dir, f"voice_test_after_{timestamp}.png")
                cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                
                # 再次检测红点
                after_dots06181 = find_red_dot_with_nms(debug_img_path)
                if after_dots06181:
                    self.log(f"仍有 {len(after_dots06181)} 个语音消息红点未处理")
                    
                    # 如果红点数量没有减少，尝试不同策略
                    if len(after_dots06181) >= len(voice_red_dots06181):
                        self.log("红点数量未减少，请先调整参数")
                else:
                    self.log("所有语音消息已处理完成")
            else:
                self.log("未检测到语音消息红点")
            
            # 更新预览
            self.update_preview()
        except Exception as e:
            self.log(f"测试语音功能出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
    
    def show_conversation_history(self):
        """显示对话历史记录"""
        # 创建对话历史界面
        history_ui = HistoryUI(self.root, self.conversations, 
                               save_callback=self.storage_manager.save_conversation)
        history_ui.show_window()
    
    def load_conversations(self):
        """加载所有对话历史"""
        conversations, message_counts = self.storage_manager.load_all_conversations()
        self.conversations = conversations
        self.contact_message_counts = message_counts
        self.log(f"已加载{len(self.conversations)}个联系人的聊天历史")
    
    def save_api_settings(self, api_settings):
        """保存API设置"""
        # 将新的设置合并到当前设置中
        self.settings.update(api_settings)
        
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("API设置已保存")
        
        # 输出调试信息
        self.debug_log(f"保存的API设置: provider={self.settings.get('provider')}")
        if self.settings.get('provider') == 'openai':
            self.debug_log(f"OpenAI API设置: URL={self.settings.get('api_base_url')}, Model={self.settings.get('model_name')}")
        else:  # coze
            self.debug_log(f"Coze Space API设置: Bot ID={self.settings.get('bot_id')}")
    
    def save_prompt_templates(self, templates_data):
        """保存提示词模板设置"""
        # 更新设置对象中的提示词模板和默认系统提示词
        self.settings['prompt_templates'] = templates_data.get('prompt_templates', [])
        self.settings['default_system_prompt'] = templates_data.get('default_system_prompt', self.settings.get('system_prompt', ''))
        
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("提示词模板设置已保存")
    
    def save_daily_summary_settings(self, daily_summary_settings):
        """保存每日总结设置"""
        # 更新设置字典中的每日总结相关设置
        self.settings.update(daily_summary_settings)
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("每日总结设置已保存")
        
        # 重新调度每日总结任务
        self.schedule_daily_summary()
    
    def schedule_daily_summary(self):
        """安排每日总结定时任务"""
        from datetime import datetime, timedelta
        import time
        
        # 取消之前的调度
        if hasattr(self, 'scheduled_summary_id') and self.scheduled_summary_id:
            self.root.after_cancel(self.scheduled_summary_id)
            self.scheduled_summary_id = None
        
        # 获取每日总结时间
        daily_summary_time = self.settings.get('daily_summary_time', '18:00')
        
        try:
            # 解析时间
            hour, minute = map(int, daily_summary_time.split(':'))
            
            # 当前时间
            now = datetime.now()
            
            # 今天的总结时间点
            target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果今天的时间已经过了，就设置为明天
            if now > target_time:
                target_time += timedelta(days=1)
            
            # 计算到目标时间的毫秒数
            delay_ms = int((target_time - now).total_seconds() * 1000)
            
            # 设置定时器
            self.scheduled_summary_id = self.root.after(delay_ms, self.execute_scheduled_summary)
            
            # 记录日志
            next_run = target_time.strftime('%Y-%m-%d %H:%M:%S')
            self.log(f"每日总结已调度，将于 {next_run} 执行")
            
        except Exception as e:
            self.log(f"调度每日总结任务失败: {str(e)}")
    
    def execute_scheduled_summary(self):
        """执行定时的每日总结任务并重新调度下一次执行"""
        self.log("执行定时的每日总结任务...")
        
        # 执行总结
        self.run_summary_now()
        
        # 重新调度下一次执行
        self.schedule_daily_summary()
    
    def run_summary_now(self):
        """立即执行每日总结"""
        self.log("正在执行每日总结...")
        
        try:
            from datetime import datetime, timedelta
            import json
            import os
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            from openai import OpenAI
            import tkinter as tk
            from tkinter import ttk
            
            # 创建一个进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("每日总结进度")
            progress_window.geometry("500x300")
            progress_window.resizable(False, False)
            
            # 窗口居中
            window_width = 500
            window_height = 300
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # 添加标题
            title_label = ttk.Label(progress_window, text="正在生成每日总结...", font=('微软雅黑', 14, 'bold'))
            title_label.pack(pady=10)
            
            # 添加进度条
            progress = ttk.Progressbar(progress_window, orient="horizontal", length=400, mode="indeterminate")
            progress.pack(pady=10)
            progress.start()
            
            # 添加当前处理状态标签
            status_label = ttk.Label(progress_window, text="准备中...", font=('微软雅黑', 10))
            status_label.pack(pady=5)
            
            # 添加日志文本框
            log_frame = ttk.Frame(progress_window)
            log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
            
            log_label = ttk.Label(log_frame, text="处理日志:", font=('微软雅黑', 10, 'bold'))
            log_label.pack(anchor=tk.W)
            
            log_text = tk.Text(log_frame, wrap=tk.WORD, height=8, width=50)
            log_text.pack(fill=tk.BOTH, expand=True)
            log_text.config(state=tk.DISABLED)  # 设置为只读
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(log_text)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            log_text.config(yscrollcommand=scrollbar.set)
            scrollbar.config(command=log_text.yview)
            
            # 更新日志的函数
            def update_log(message):
                log_text.config(state=tk.NORMAL)
                log_text.insert(tk.END, f"{message}\n")
                log_text.see(tk.END)  # 滚动到底部
                log_text.config(state=tk.DISABLED)
                progress_window.update()  # 更新窗口
            
            # 更新状态的函数
            def update_status(message):
                status_label.config(text=message)
                progress_window.update()
            
            update_log("开始执行每日总结...")
            
            # 创建一个线程来执行耗时操作
            def summary_worker():
                try:
                    # 获取24小时前的时间戳
                    now = datetime.now()
                    yesterday = now - timedelta(days=1)
                    yesterday_timestamp = yesterday.timestamp()
                    
                    # 汇总结果
                    summary_results = []
                    has_data = False
                    
                    # 遍历聊天历史目录中的所有文件
                    chat_dir = self.storage_manager.storage_dir
                    update_log(f"扫描聊天记录目录: {chat_dir}")
                    
                    # 获取所有JSON文件
                    json_files = [f for f in os.listdir(chat_dir) if f.endswith('.json')]
                    total_files = len(json_files)
                    processed_files = 0
                    
                    for filename in json_files:
                        processed_files += 1
                        contact_name = os.path.splitext(filename)[0]
                        file_path = os.path.join(chat_dir, filename)
                        
                        current_progress = int(processed_files / total_files * 100)
                        update_status(f"正在处理: {contact_name} ({processed_files}/{total_files})")
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                conversation_data = json.load(f)
                            
                            # 过滤24小时内的消息
                            recent_messages = []
                            has_recent = False
                            
                            for msg in conversation_data:
                                # 检查消息是否有时间戳字段，且时间在24小时内
                                if 'timestamp' in msg and float(msg['timestamp']) >= yesterday_timestamp:
                                    recent_messages.append(msg)
                                    has_recent = True
                            
                            # 如果有最近的消息，进行总结
                            if has_recent:
                                has_data = True
                                msg_count = len(recent_messages)
                                update_log(f"发现{msg_count}条与{contact_name}的最近聊天记录")
                                
                                # 格式化聊天内容，便于AI总结
                                chat_content = ""
                                for msg in recent_messages:
                                    if msg["role"] == "user":
                                        chat_content += f"{contact_name}：{msg.get('content', '')}\n"
                                    elif msg["role"] == "assistant":
                                        chat_content += f"AI助手：{msg.get('content', '')}\n"
                                
                                # 调用AI进行总结
                                update_status(f"正在总结{contact_name}的聊天记录...")
                                update_log(f"分析{contact_name}的聊天内容中...")
                                summary = self.summarize_chat(contact_name, chat_content)
                                
                                # 添加到汇总结果
                                summary_results.append(f"## {contact_name} 聊天总结\n\n{summary}\n\n")
                                update_log(f"✓ {contact_name}的聊天总结已完成")
                            else:
                                update_log(f"跳过{contact_name}: 24小时内无聊天记录")
                        except Exception as e:
                            update_log(f"处理{contact_name}记录时出错: {str(e)}")
                            self.log(f"处理 {contact_name} 的聊天记录时出错: {str(e)}")
                    
                    # 如果没有数据，记录信息并返回
                    if not has_data:
                        update_status("完成: 无需生成总结")
                        update_log("过去24小时内没有聊天记录，无需生成总结")
                        self.log("过去24小时内没有聊天记录，无需生成总结")
                        
                        # 设置一个定时器，5秒后关闭进度窗口
                        progress_window.after(5000, progress_window.destroy)
                        return
                    
                    # 生成日报内容
                    update_status("正在生成日报...")
                    report_title = f"# 微信聊天日报 - {now.strftime('%Y-%m-%d')}\n\n"
                    report_content = report_title + "\n".join(summary_results)
                    
                    # 保存日报文件
                    report_dir = "daily_reports"
                    os.makedirs(report_dir, exist_ok=True)
                    report_file = os.path.join(report_dir, f"daily_report_{now.strftime('%Y%m%d')}.md")
                    
                    with open(report_file, 'w', encoding='utf-8') as f:
                        f.write(report_content)
                        
                    update_log(f"日报已生成: {report_file}")
                    self.log(f"日报已生成: {report_file}")
                    
                    # 发送邮件
                    update_status("正在发送邮件报告...")
                    update_log("正在发送邮件...")
                    self.send_summary_email(report_content, now.strftime('%Y-%m-%d'))
                    
                    update_status("每日总结已完成")
                    update_log("每日总结已全部完成，邮件已发送!")
                    self.log("每日总结已完成，日报已发送")
                    
                    # 停止进度条动画
                    progress.stop()
                    progress.config(mode="determinate", value=100)
                    
                    # 添加关闭按钮
                    close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                    close_btn.pack(pady=10)
                    
                except Exception as e:
                    self.log(f"执行每日总结时出错: {str(e)}")
                    import traceback
                    self.log(f"错误详情: {traceback.format_exc()}")
                    
                    # 如果进度窗口存在，显示错误
                    if 'progress_window' in locals() and progress_window.winfo_exists():
                        update_status("执行过程中出错")
                        update_log(f"错误: {str(e)}")
                        
                        # 停止进度条动画
                        progress.stop()
                        
                        # 添加关闭按钮
                        close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                        close_btn.pack(pady=10)
            
            # 启动工作线程
            summary_thread = threading.Thread(target=summary_worker, daemon=True)
            summary_thread.start()
            
        except Exception as e:
            self.log(f"启动每日总结线程时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
    
    def summarize_chat(self, contact_name, chat_content):
        """使用AI总结聊天内容"""
        try:
            # Import OpenAI client
            from openai import OpenAI
            
            # 获取API密钥和设置
            provider = self.settings.get('provider', 'openai')
            
            if provider == 'openai':
                api_key = self.settings.get('api_key')
                api_base_url = self.settings.get('api_base_url')
                model_name = self.settings.get('model_name')
            else:
                # 对于其他提供商，如Coze，这里应该返回错误
                return "总结失败: 目前仅支持OpenAI API进行总结"
                
            if not api_key:
                return "无法生成总结：未配置API密钥"
                
            # 从设置中获取总结提示词，如果没有则使用默认提示词
            summary_prompt = self.settings.get('daily_summary_prompt', '')
            if not summary_prompt:
                # 默认提示词
                summary_prompt = f"""
你是一个聊天总结专家，有这丰富的识别问题和提炼要点的能力。

请对以下聊天对话内容进行总结：
{{chat_content}}

总结输出格式：
1.用户主要的疑问和顾虑
2.用户的性格和情绪
3.用户的意向
4.整体总结
"""
            
            # 替换提示词中的{chat_content}占位符
            system_prompt = summary_prompt.replace("{chat_content}", chat_content)

            # 调用OpenAI API
            client = OpenAI(api_key=api_key, base_url=api_base_url)
            
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": "请你总结"}
                ],
                stream=False
            )
            print(system_prompt)
            return response.choices[0].message.content
            
        except Exception as e:
            self.log(f"AI总结时出错: {str(e)}")
            return f"总结失败: {str(e)}"
    
    def send_summary_email(self, report_content, date_str):
        """发送总结邮件"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            
            # 获取邮件设置
            mail_host = self.settings.get('mail_host', '')
            mail_user = self.settings.get('mail_user', '')
            mail_pass = self.settings.get('mail_pass', '')
            receivers_str = self.settings.get('receivers', '')
            
            if not mail_host or not mail_user or not mail_pass or not receivers_str:
                self.log("邮件配置不完整，无法发送")
                return
                
            # 解析收件人列表
            receivers = [email.strip() for email in receivers_str.split(',') if email.strip()]
            
            if not receivers:
                self.log("未指定收件人，无法发送邮件")
                return
            
            # 预处理Markdown内容，直接将数字+点格式的内容转换为HTML格式
            # 这样可以保持原有的编号而不会被Markdown解析器误解为列表
            lines = report_content.split('\n')
            processed_lines = []
            
            for line in lines:
                # 检查行是否以数字和点开头 (如 "1."、"2.", 等)
                match = re.match(r'^(\d+)\.\s*(.*)', line.strip())
                if match:
                    # 保留原格式但不使用Markdown的有序列表
                    num, text = match.groups()
                    processed_lines.append(f"{num}. {text}")
                else:
                    processed_lines.append(line)
            
            processed_content = '\n'.join(processed_lines)
            
            # 不使用标准Markdown解析，而是直接制作HTML
            html_parts = []
            in_paragraph = False
            
            for line in processed_lines:
                line = line.strip()
                
                # 标题处理
                if line.startswith('# '):
                    if in_paragraph:
                        html_parts.append('</p>')
                        in_paragraph = False
                    html_parts.append(f'<h1>{line[2:]}</h1>')
                elif line.startswith('## '):
                    if in_paragraph:
                        html_parts.append('</p>')
                        in_paragraph = False
                    html_parts.append(f'<h2>{line[3:]}</h2>')
                # 数字+点处理
                elif re.match(r'^\d+\.\s+', line):
                    match = re.match(r'^(\d+)\.\s+(.*)', line)
                    num, text = match.groups()
                    if in_paragraph:
                        html_parts.append('</p>')
                        in_paragraph = False
                    html_parts.append(f'<p><strong>{num}.</strong> {text}</p>')
                # 空行处理
                elif not line:
                    if in_paragraph:
                        html_parts.append('</p>')
                        in_paragraph = False
                    html_parts.append('<br/>')
                # 普通文本处理
                else:
                    if not in_paragraph:
                        html_parts.append('<p>')
                        in_paragraph = True
                    else:
                        html_parts.append('<br/>')
                    html_parts.append(line)
            
            if in_paragraph:
                html_parts.append('</p>')
            
            html_content = '\n'.join(html_parts)
            
            # HTML邮件模板
            html_template = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>微信聊天日报 - {date_str}</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 900px;
                        margin: 0 auto;
                        padding: 20px;
                    }}
                    h1 {{
                        color: #2b579a;
                        border-bottom: 2px solid #2b579a;
                        padding-bottom: 10px;
                        margin-bottom: 20px;
                    }}
                    h2 {{
                        color: #5c7db0;
                        margin-top: 30px;
                        padding-bottom: 5px;
                        border-bottom: 1px solid #ddd;
                    }}
                    ol, ul {{
                        margin-left: 20px;
                        padding-left: 20px;
                    }}
                    li {{
                        margin: 8px 0;
                    }}
                    p {{
                        margin: 10px 0;
                    }}
                    .summary-container {{
                        background-color: #f9f9f9;
                        border-left: 4px solid #2b579a;
                        padding: 15px;
                        margin: 20px 0;
                    }}
                    .footer {{
                        margin-top: 40px;
                        padding-top: 10px;
                        border-top: 1px solid #ddd;
                        font-size: 0.9em;
                        color: #777;
                        text-align: center;
                    }}
                </style>
            </head>
            <body>
                <div class="content">
                    {html_content}
                </div>
                <div class="footer">
                    <p>聊天助手自动生成 - {date_str}</p>
                </div>
            </body>
            </html>
            """
                
            # 创建一个带有附件的邮件实例
            message = MIMEMultipart()
            message['From'] = Header(f"{mail_user}", 'utf-8')
            message['To'] = Header(f"{', '.join(receivers)}", 'utf-8')
            message['Subject'] = Header(f"聊天日报 - {date_str}", 'utf-8')
            
            # 添加HTML内容
            message.attach(MIMEText(html_template, 'html', 'utf-8'))
            
            # 发送邮件
            try:
                smtp_obj = smtplib.SMTP(mail_host)
                smtp_obj.login(mail_user, mail_pass)
                smtp_obj.sendmail(mail_user, receivers, message.as_string())
                self.log("邮件发送成功")
            except smtplib.SMTPException as e:
                self.log(f"邮件发送失败: {str(e)}")
                
        except Exception as e:
            self.log(f"发送邮件时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
    
    def get_system_prompt_for_contact(self, contact_name):
        """根据联系人名称获取系统提示词"""
        self.debug_log(f"开始为联系人'{contact_name}'匹配系统提示词")
        
        # 如果设置界面已初始化，使用其方法获取匹配的系统提示词
        if hasattr(self, 'settings_ui') and self.settings_ui:
            self.debug_log("通过settings_ui获取系统提示词")
            prompt = self.settings_ui.get_system_prompt_for_contact(contact_name)
            # 确保prompt不为None
            if prompt is None:
                prompt = ""
                self.debug_log("警告：从settings_ui获取到的prompt为None，已设置为空字符串")
            self.debug_log(f"从settings_ui获取到提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
            return prompt
            
        # 否则，自行进行匹配
        prompt_templates = self.settings.get('prompt_templates', [])
        self.debug_log(f"从设置中获取提示词模板，共{len(prompt_templates)}个")
        
        # 如果联系人名称存在，尝试匹配关键词
        if contact_name:
            for template in prompt_templates:
                keyword = template.get('keyword', '')
                if keyword and keyword in contact_name:
                    prompt = template.get('prompt', '')  # 添加默认空字符串
                    if prompt is None:
                        prompt = ""
                    self.debug_log(f"联系人'{contact_name}'匹配到关键词'{keyword}'，使用对应的提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
                    return prompt
                else:
                    self.debug_log(f"关键词'{keyword}'与联系人'{contact_name}'不匹配")
                    
        # 没有匹配到关键词，使用默认系统提示词
        default_prompt = self.settings.get('default_system_prompt', '你是一个有用的助手。请用简短、友好的方式回答问题。')
        # 确保default_prompt不为None
        if default_prompt is None:
            default_prompt = '你是一个有用的助手。请用简短、友好的方式回答问题。'
        
        self.debug_log(f"未匹配到关键词，使用默认提示词: {default_prompt[:30]}..." if default_prompt and len(default_prompt) > 30 else default_prompt)
        return default_prompt
    
    def save_conversation(self, contact_name, conversation_data):
        """保存指定联系人的对话历史"""
        result = self.storage_manager.save_conversation(contact_name, conversation_data)
        if result:
            self.debug_log(f"已保存'{contact_name}'的聊天历史")
        return result
        
    def switch_contact(self, new_contact_name):
        """切换当前联系人"""
        if not new_contact_name:
            self.debug_log("未提供新联系人名称，无法切换")
            return
            
        # 如果当前有联系人，保存当前会话
        if self.current_contact:
            # 只有在不是None的情况下才保存，以避免保存空会话
            if self.conversation_history:
                self.save_conversation(self.current_contact, self.conversation_history)
                self.debug_log(f"已保存当前会话'{self.current_contact}'的状态，共{len(self.conversation_history)}条消息")
        
        # 记录旧联系人名称
        old_contact = self.current_contact
        
        # 切换到新联系人
        self.current_contact = new_contact_name
        
        # 加载新联系人的聊天历史
        self.conversation_history = self.conversations.get(new_contact_name, [])
        
        # 确保历史会话中的消息回复状态正确
        if self.conversation_history:
            last_msg_role = None
            for i, msg in enumerate(self.conversation_history):
                # 确保用户消息后面跟着的助手消息将用户消息标记为已回复
                if msg["role"] == "user" and not msg.get("is_replied", False):
                    # 检查后面是否有助手回复
                    if i + 1 < len(self.conversation_history) and self.conversation_history[i + 1]["role"] == "assistant":
                        msg["is_replied"] = True
                        self.debug_log(f"在切换联系人时将用户消息标记为已回复: {msg['content']}")
                
                last_msg_role = msg["role"]
        
        # 重置last_messages，以便下次检测能正确识别所有消息
        self.last_messages = []
        self.last_message_count = 0
        
        # 更新消息计数
        self.contact_message_counts[new_contact_name] = len(self.conversation_history)
        
        self.log(f"已切换到联系人'{new_contact_name}'，{'加载了'+str(len(self.conversation_history))+'条历史消息' if self.conversation_history else '没有历史消息'}")
        
        # 返回切换结果和旧联系人名称
        return True, old_contact
    
    def process_chat_area(self):
        """处理聊天区域，识别消息并处理回复"""
        # 如果正在切换联系人，则跳过聊天区域处理
        if self.contact_switching:
            self.debug_log("正在切换联系人，暂停聊天区域处理")
            return
            
        # 检查是否已请求停止监控 - 将检查移到方法开始处
        if self.stop_requested06141:
            self.debug_log("检测到停止请求，跳过聊天区域处理")
            return
            
        try:
            # 截取屏幕区域
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            self.debug_log(f"截图完成，尺寸: {screenshot.shape[1]}x{screenshot.shape[0]}")
            
            # 保存当前截图用于调试
            if self.debug_mode:
                debug_dir = "debug_screenshots"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_img_path = os.path.join(debug_dir, f"screenshot_{timestamp}.png")
                cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                self.debug_log(f"已保存调试截图: {debug_img_path}")
            
            # 语音消息红点检测和处理逻辑06181
            from utils.voice_read_detector import find_red_dot_with_nms
            import pyautogui
            
            # 保存截图用于检测
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            debug_img_path = os.path.join(debug_dir, f"voice_detection_{timestamp}.png")
            cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            
            self.debug_log("开始检测语音消息红点...")
            voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
            
            if voice_red_dots06181:
                self.debug_log(f"找到 {len(voice_red_dots06181)} 个语音消息红点")
                
                # 获取截图尺寸
                img_height06181, img_width06181 = screenshot.shape[:2]
                self.debug_log(f"截图尺寸: 宽={img_width06181}, 高={img_height06181}")
                
                # 获取聊天区域尺寸
                chat_width06181 = x2 - x1
                chat_height06181 = y2 - y1
                self.debug_log(f"聊天区域尺寸: 宽={chat_width06181}, 高={chat_height06181}")
                
                # 计算缩放比例 (如果截图尺寸与聊天区域不一致)
                scale_x06181 = chat_width06181 / img_width06181
                scale_y06181 = chat_height06181 / img_height06181
                self.debug_log(f"缩放比例: x={scale_x06181}, y={scale_y06181}")
                
                # 处理所有语音红点直到没有为止
                processed_count06181 = 0
                max_attempts06181 = 5  # 防止无限循环
                
                while voice_red_dots06181 and processed_count06181 < max_attempts06181:
                    # 检查是否已请求停止监控
                    if self.stop_requested06141:
                        self.debug_log("检测到停止请求，中断语音消息红点处理")
                        break
                        
                    # 获取第一个红点的坐标
                    dot_x06181, dot_y06181 = voice_red_dots06181[0]
                    self.debug_log(f"处理语音红点#{processed_count06181+1}，截图中的相对坐标: ({dot_x06181}, {dot_y06181})")
                    
                    # 应用缩放比例
                    scaled_x06181 = int(dot_x06181 * scale_x06181)
                    scaled_y06181 = int(dot_y06181 * scale_y06181)
                    self.debug_log(f"缩放后坐标: ({scaled_x06181}, {scaled_y06181})")
                    
                    # 计算语音气泡位置 (红点左侧约100像素处)
                    voice_bubble_x06181 = scaled_x06181 - int(100 * scale_x06181)
                    # 限制不要超出屏幕边界
                    voice_bubble_x06181 = max(10, voice_bubble_x06181)
                    
                    # 转换为屏幕绝对坐标
                    abs_x06181 = x1 + voice_bubble_x06181
                    abs_y06181 = y1 + scaled_y06181
                    
                    # 记录详细的坐标信息用于调试
                    self.debug_log(f"聊天区域在屏幕上的位置: ({x1}, {y1}, {x2}, {y2})")
                    self.debug_log(f"点击语音消息，屏幕绝对坐标: ({abs_x06181}, {abs_y06181})")
                    
                    # 确保坐标在有效范围内
                    if x1 <= abs_x06181 <= x2 and y1 <= abs_y06181 <= y2:
                        # 获取鼠标操作间隔时间
                        mouse_interval = 1.0  # 默认值
                        if hasattr(self.ui, 'mouse_interval_var'):
                            mouse_interval = self.ui.mouse_interval_var.get()
                        
                        # 移动鼠标并点击
                        time.sleep(mouse_interval)
                        mouse = Controller()
                        pyautogui.moveTo(abs_x06181, abs_y06181)
                        mouse.position = (abs_x06181, abs_y06181)
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                        new_abs_x06181 = abs_x06181 + 15
                        new_abs_y06181 = abs_y06181 + 15
                        mouse.position = (new_abs_x06181, new_abs_y06181)
                        time.sleep(mouse_interval)
                        mouse.click(Button.left)
                        time.sleep(mouse_interval)
                        pyautogui.moveTo(new_abs_x06181, new_abs_y06181-50)

                    else:
                        self.debug_log(f"警告：计算出的坐标 ({abs_x06181}, {abs_y06181}) 超出了聊天区域范围，调整点击位置")
                        # 调整到区域内的安全位置
                        safe_x = min(max(abs_x06181, x1 + 10), x2 - 10)
                        safe_y = min(max(abs_y06181, y1 + 10), y2 - 10)
                        self.debug_log(f"调整后的安全坐标: ({safe_x}, {safe_y})")

                        # 获取鼠标操作间隔时间
                        mouse_interval = 1.0  # 默认值
                        if hasattr(self.ui, 'mouse_interval_var'):
                            mouse_interval = self.ui.mouse_interval_var.get()
                            
                        time.sleep(mouse_interval)
                        mouse = Controller()
                        pyautogui.moveTo(safe_x, safe_y)
                        mouse.position = (safe_x, safe_y)
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                        new_safe_x = safe_x + 15
                        new_safe_y = safe_y + 15
                        mouse.position = (new_safe_x, new_safe_y)
                        time.sleep(mouse_interval)
                        mouse.click(Button.left)
                        time.sleep(mouse_interval)
                        pyautogui.moveTo(new_safe_x, new_safe_y-50)

                    # 等待语音消息处理完成
                    # 获取等待转文字时间
                    voice_wait = 6.0  # 默认值
                    if hasattr(self.ui, 'voice_wait_var'):
                        voice_wait = self.ui.voice_wait_var.get()
                    self.debug_log(f"等待语音转文字完成，等待时间: {voice_wait}秒")
                    
                    # 分段等待，每次检查是否有停止请求
                    wait_segments = 10  # 将等待时间分为10段
                    segment_time = voice_wait / wait_segments
                    for _ in range(wait_segments):
                        # 检查是否已请求停止监控
                        if self.stop_requested06141:
                            self.debug_log("检测到停止请求，中断语音消息等待")
                            break
                        time.sleep(segment_time)
                    
                    # 如果已请求停止，跳出循环
                    if self.stop_requested06141:
                        self.debug_log("检测到停止请求，中断语音消息红点处理")
                        break

                    # mac
                    if platform.system() == "Darwin":
                        pyautogui.scroll(-500)
                    else:
                        pyautogui.scroll(500)
                    
                    processed_count06181 += 1
                    
                    # 检查是否已请求停止监控
                    if self.stop_requested06141:
                        self.debug_log("检测到停止请求，中断语音消息红点处理")
                        break
                    
                    # 再次截图并检测红点
                    screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    debug_img_path = os.path.join(debug_dir, f"voice_detection_after_{timestamp}.png")
                    cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    
                    # 再次检测红点
                    prev_count = len(voice_red_dots06181)
                    voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
                    curr_count = len(voice_red_dots06181)
                    
                    if voice_red_dots06181:
                        self.debug_log(f"仍有 {curr_count} 个语音消息红点待处理 (之前有 {prev_count} 个)")
                        
                        # 如果红点数量没有减少，尝试其他策略
                        if curr_count >= prev_count and processed_count06181 > 1:
                            self.debug_log(f"警告：多次处理后红点数量未减少，请先测试语音消息红点处理")

                    else:
                        self.debug_log(f"所有语音消息已处理完成，共处理 {processed_count06181} 个语音红点")
                
                if processed_count06181 >= max_attempts06181 and voice_red_dots06181:
                    self.debug_log(f"达到最大尝试次数 ({max_attempts06181})，终止语音红点处理")
            else:
                self.debug_log("未检测到语音消息红点")
            
            # 检查是否已请求停止监控
            if self.stop_requested06141:
                self.debug_log("检测到停止请求，跳过OCR处理")
                return
                
            # 构建OCR参数
            ocr_params = {
                'debug_mode': self.debug_mode,
                'mask_green_bubbles': self.ui.mask_green_var.get(),
                'current_contact': self.current_contact,
                'screen_center_x': self.screen_center_x,
                'x1': x1,
                'y1': y1,
                'y2': y2,
                'center_margin': self.ui.center_margin_var.get(),
                'line_spacing_threshold': self.ui.line_spacing_var.get(),
                'conversation_history': self.conversation_history,
                'green_r': self.ui.green_r_var.get(),
                'green_g': self.ui.green_g_var.get(),
                'green_b': self.ui.green_b_var.get(),
                'green_tol': self.ui.green_tol_var.get(),
                'green_min_area': self.ui.green_min_area_var.get(),
                'text_color_enabled': self.ui.text_color_enabled_var.get(),
                'text_color_r': self.ui.text_color_r_var.get(),
                'text_color_g': self.ui.text_color_g_var.get(),
                'text_color_b': self.ui.text_color_b_var.get(),
                'text_color_tol': self.ui.text_color_tol_var.get()
            }
            
            # OCR识别文本
            self.debug_log("开始OCR识别...")
            start_time = time.time()
            messages, contact_name = self.message_extractor.extract_messages(screenshot, ocr_params)
            ocr_time = time.time() - start_time
            
            self.debug_log(f"OCR识别完成，耗时: {ocr_time:.2f}秒")
            self.debug_log(f"OCR结果: 识别到{len(messages)}条文字")
            
            # 如果没有识别到消息，检查是否需要重试
            if not messages:
                self.debug_log("未识别到任何消息，可能需要调整OCR参数或截图区域")
                # 记录详细日志便于调试
                h, w = screenshot.shape[:2]
                self.debug_log(f"截图尺寸: {w}x{h}，聊天区域: {self.chat_area}")
                return
            
            # 检查是否需要更新联系人名称 - 只有在明确触发的情况下才更新
            # 联系人切换标志由monitor_contact_area在点击红点后设置
            if self.contact_switch_triggered and contact_name:
                self.debug_log(f"检测到联系人切换标志已触发，且OCR识别到联系人名称: {contact_name}")
                # 如果联系人名称发生变化，则切换联系人
                if self.current_contact != contact_name:
                    self.debug_log(f"联系人名称变更: '{self.current_contact}' -> '{contact_name}'")
                    self.switch_contact(contact_name)
                # 重置联系人切换标志
                self.contact_switch_triggered = False
            
            # 处理新消息，发送回复
            self.detect_and_reply_to_messages(messages)
            
        except Exception as e:
            self.log(f"处理聊天区域时出错: {str(e)}")
            import traceback
            self.debug_log(traceback.format_exc())
    
    def get_filtered_conversation_history(self, contact_name):
        """
        根据最后总结时间戳过滤会话历史，只返回最后总结之后的消息
        如果没有总结记录，则返回完整历史
        """
        # 获取最后的总结记录
        last_summary = self.chat_summary_processor06151.get_last_summary(contact_name)
        
        if not last_summary:
            self.debug_log(f"联系人 {contact_name} 没有总结记录，使用完整会话历史")
            return self.conversation_history
        
        # 获取最后总结时间戳
        last_summary_time = last_summary.get("last_time_summary", 0)
        self.debug_log(f"联系人 {contact_name} 最后总结时间戳: {last_summary_time}")
        
        # 过滤出最后总结之后的消息
        filtered_history = []
        
        # 添加最后总结之后的消息
        for msg in self.conversation_history:
            if msg.get("timestamp", 0) > last_summary_time:
                filtered_history.append(msg)
        
        self.debug_log(f"过滤后的会话历史共 {len(filtered_history)} 条消息，原有 {len(self.conversation_history)} 条")
        return filtered_history

    def detect_and_reply_to_messages(self, messages):
        """检测新消息并处理回复"""
        try:
            # 如果已经请求停止监控，不再检测新消息
            if getattr(self, 'stop_requested06141', False):
                self.debug_log("检测到停止请求，跳过新消息检测")
                return
                
            # 检测新消息
            self.debug_log(f"开始检测新消息（当前历史消息数量：{self.last_message_count}）")
            new_messages = self.detect_new_messages(messages)
            
            if new_messages:
                self.log(f"【重要】检测到{len(new_messages)}条新的未回复消息！")
                
                # 添加新消息到历史记录（但只添加用户消息，因为new_messages现在只包含用户消息）
                for msg in new_messages:
                    role = msg["role"]
                    content = msg["content"]
                    role_text = "用户" if role == "user" else "助手"
                    self.log(f"【新消息】[{self.current_contact}-{role_text}]: {content}")
                    # 将新消息添加到对话历史
                    self.conversation_history.append(msg)
                
                # 判断是否有未回复的用户消息
                if new_messages:
                    # 有未回复的用户消息，需要生成回复
                    self.log(f"【需要回复】检测到{len(new_messages)}条未回复的用户消息，准备生成回复")
                    
                    # 设置回复标志
                    self.is_replying = True
                    self.reply_start_time = time.time()
                    
                    # 重置停止请求标志
                    self.stop_requested06141 = False
                    
                    # 获取针对当前联系人的系统提示词
                    system_prompt = self.get_system_prompt_for_contact(self.current_contact)
                    # 确保system_prompt不为None，如果是None则使用空字符串
                    if system_prompt is None:
                        system_prompt = ""
                        self.debug_log("警告：获取到的system_prompt为None，已设置为空字符串")

                    system_prompt = system_prompt.replace("{联系人名称}", self.current_contact)

                    # 获取之前对话总结
                    summary_file = f"summary_chat/{self.current_contact}.json"
                    if os.path.exists(summary_file):
                        with open(summary_file, 'r', encoding='utf-8') as f:
                            summary_data = json.load(f)
                            summary_text = summary_data[-1]["summary_content"]
                    else:
                        summary_text = ''
                    
                    system_prompt = system_prompt.replace("{之前对话总结}", summary_text)

                    print("系统提示词：",system_prompt)
                    
                    self.debug_log(f"联系人'{self.current_contact}'使用系统提示词: {system_prompt[:50]}..." if system_prompt and len(system_prompt) > 50 else system_prompt)
                    
                    # 创建AI聊天实例
                    api_settings = {
                        'provider': self.settings.get('provider', 'openai'),
                        'api_key': self.settings.get('api_key', ''),
                        'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
                        'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
                        'bot_id': self.settings.get('bot_id', ''),
                        'token': self.settings.get('token', ''),
                        'system_prompt': system_prompt,
                        'delay': self.ui.delay_var.get(),
                        'debug_mode': self.debug_mode
                    }
                    self.debug_log(f"测试回复使用API提供商: {api_settings['provider']}")
                    ai_chat = AiChat(api_settings, stop_flag=lambda: self.stop_requested06141)
                    
                    # 获取过滤后的会话历史（只包含最后总结之后的消息）
                    filtered_history = self.get_filtered_conversation_history(self.current_contact)
                    
                    # 生成回复
                    reply_list = ai_chat.generate_reply(filtered_history, self.input_area)
                    reply = reply_list[-1]["content"]
                    # 模拟输入回复
                    ai_chat.type_reply(reply, self.input_area)
                    

                    self.conversation_history += reply_list
                    self.log(f"【助手回复】已添加到对话历史: {reply[:50]}..." if len(reply) > 50 else reply)
                    
                    # 重要：标记所有新消息为已回复
                    for msg in new_messages:
                        msg["is_replied"] = True
                    self.log(f"已将{len(new_messages)}条用户消息标记为已回复")
                    
                    # 在原始messages列表中也标记这些消息为已回复
                    for msg in messages:
                        if msg["role"] == "user" and not msg.get("is_replied", False):
                            # 检查这条消息是否在new_messages中
                            for new_msg in new_messages:
                                if msg["content"] == new_msg["content"]:
                                    msg["is_replied"] = True
                                    self.debug_log(f"在原始消息列表中将消息标记为已回复: {msg['content']}")
                                    break
                    
                    # 清除回复标志
                    self.is_replying = False
                else:
                    self.log(f"【无需回复】没有新的未回复用户消息，跳过回复")
                
                # 更新当前联系人的会话记录
                self.conversations[self.current_contact] = self.conversation_history
                
                # 保存更新后的会话历史
                self.save_conversation(self.current_contact, self.conversation_history)
            else:
                self.debug_log("未检测到新的未回复消息")
            
            # 更新上一次消息记录
            self.last_messages = messages
            self.last_message_count = len(messages)
            
        except Exception as e:
            self.log(f"检测新消息时出错: {str(e)}")
            import traceback
            self.debug_log(traceback.format_exc())
    
    def detect_new_messages(self, current_messages):
        """检测新消息，比较当前消息和历史消息"""
        # 如果当前没有联系人，无法检测新消息
        if not self.current_contact:
            self.debug_log("当前没有活跃联系人，无法检测新消息")
            return []
            
        # 是否刚刚发生了联系人切换（通过last_messages为空且conversation_history不为空判断）
        just_switched = not self.last_messages and self.conversation_history and len(self.conversation_history) > 0
        if just_switched:
            self.debug_log("检测到刚刚切换联系人，将重新比较全部消息")
            
        # 比较当前消息和之前的消息，找出新消息
        if not self.last_messages or just_switched:
            self.debug_log("首次检测或刚切换联系人，设置基准消息并检查是否有用户消息需要回复")
            
            # 找出当前消息中未在历史记录中的用户消息
            new_user_messages = []
            
            # 检查当前消息中是否有未在历史记录中出现的用户消息
            history_content = {msg["content"] for msg in self.conversation_history}
            for msg in current_messages:
                if msg["role"] == "user" and not msg.get("is_replied", False) and msg["content"] not in history_content:
                    new_user_messages.append(msg)
                    self.debug_log(f"找到未在历史记录中的用户消息: {msg['content']}")
            
            if new_user_messages:
                self.debug_log(f"首次检测到{len(new_user_messages)}条未回复的用户消息，将被处理")
                
                # 如果有多条新消息，只处理最后一条，避免一次性回复多条
                if len(new_user_messages) > 1:
                    last_user_message = new_user_messages[-1]
                    self.debug_log(f"选择最后一条用户消息进行回复: {last_user_message['content']}")
                    new_user_messages = [last_user_message]
                
                # 更新历史消息和计数
                self.last_messages = current_messages
                self.last_message_count = len(current_messages)
                
                # 返回用户消息，使其可以被回复
                return new_user_messages
            else:
                self.debug_log("首次检测未发现未回复的用户消息")
                self.last_messages = current_messages
                self.last_message_count = len(current_messages)
                return []
        
        new_messages = []
        
        # 更复杂的检测方法：检查当前消息是否包含新的内容
        if len(current_messages) > self.last_message_count:
            # 有新消息加入
            temp_new_messages = current_messages[self.last_message_count:]
            self.log(f"检测到新消息: 当前消息数({len(current_messages)}) > 历史消息数({self.last_message_count})")
            
            # 过滤已经由AI生成的回复消息和已回复的用户消息，只保留未回复的用户消息
            for msg in temp_new_messages:
                # 只添加未回复的用户消息
                if msg["role"] == "user" and not msg.get("is_replied", False):
                    # 检查是否已经在历史记录中
                    is_duplicate = False
                    for hist_msg in self.conversation_history:
                        if hist_msg["content"] == msg["content"] and hist_msg["role"] == "user":
                            is_duplicate = True
                            # 如果历史记录中的消息已标记为已回复，则当前消息也应标记为已回复
                            if hist_msg.get("is_replied", False):
                                msg["is_replied"] = True
                                self.debug_log(f"  将当前消息标记为已回复(与历史记录一致): {msg['content']}")
                            break
                    
                    if not is_duplicate:
                        # 未回复的用户消息添加到新消息列表
                        new_messages.append(msg)
                        self.debug_log(f"  添加未回复的用户新消息: {msg['content']}")
                    else:
                        if not msg.get("is_replied", False):
                            self.debug_log(f"  消息内容已存在于历史记录中，但标记为未回复: {msg['content']}")
                        else:
                            self.debug_log(f"  跳过已存在于历史记录的用户消息: {msg['content']}")
                elif msg["role"] == "user" and msg.get("is_replied", False):
                    # 跳过已回复的用户消息
                    self.debug_log(f"  跳过已回复的用户消息: {msg['content']}")
                elif msg["role"] == "assistant":
                    # 对于助手消息，只记录，不添加到new_messages
                    self.debug_log(f"  跳过助手消息: {msg['content']}，助手消息只在生成回复时添加")
                elif msg["role"] == "tool":
                    self.debug_log(f"  跳过工具消息: {msg['content']}，工具消息只在生成回复时添加")
            
            self.debug_log(f"联系人'{self.current_contact}'的新增消息 (仅未回复用户消息): {len(new_messages)}")
            for i, msg in enumerate(new_messages):
                self.debug_log(f"  新增{i+1}: {msg['content']} (角色: {msg['role']})")
        else:
            # 比较消息内容
            self.debug_log("消息数量无变化，比较内容差异")
            
            # 创建上次消息内容的集合，用于快速查找
            last_contents = {msg["content"] for msg in self.last_messages}
            
            # 创建历史记录内容的集合，包含用户和助手的消息
            history_contents = {msg["content"] for msg in self.conversation_history}
            
            # 创建历史用户消息的字典，用于检查回复状态
            history_user_msgs = {}
            for msg in self.conversation_history:
                if msg["role"] == "user":
                    history_user_msgs[msg["content"]] = msg.get("is_replied", False)
            
            # 创建历史助手消息内容的集合，用于参考
            history_assistant_contents = {
                msg["content"] for msg in self.conversation_history 
                if msg["role"] == "assistant"
            }
            
            for msg in current_messages:
                # 检查是否是新消息（不在上次消息中）且不在历史记录中
                if msg["content"] not in last_contents:
                    # 新的内容，但只添加未回复的用户消息
                    if msg["role"] == "user":
                        # 检查是否在历史记录中，以及历史记录中的回复状态
                        if msg["content"] in history_user_msgs:
                            # 如果历史记录中已标记为已回复，则当前消息也应标记为已回复
                            if history_user_msgs[msg["content"]]:
                                msg["is_replied"] = True
                                self.debug_log(f"  将消息标记为已回复(与历史记录一致): {msg['content']}")
                            else:
                                # 历史记录中标记为未回复，保持当前状态
                                if not msg.get("is_replied", False):
                                    new_messages.append(msg)
                                    self.debug_log(f"  添加历史记录中未回复的用户消息: {msg['content']}")
                        elif not msg.get("is_replied", False) and msg["content"] not in history_contents:
                            # 完全新的未回复消息
                            new_messages.append(msg)
                            self.log(f"联系人'{self.current_contact}'检测到新的未回复用户消息: {msg['content']}")
                    elif msg["role"] == "assistant":
                        # 对于助手消息，只记录，不添加到new_messages
                        if msg["content"] in history_assistant_contents:
                            self.debug_log(f"  跳过已存在的助手回复: {msg['content']}")
                        else:
                            self.debug_log(f"  跳过新助手消息: {msg['content']}，助手消息只在生成回复时添加")
        
        # 更新联系人消息计数
        self.contact_message_counts[self.current_contact] = len(current_messages)
        
        return new_messages

    def get_settings(self):
        """获取当前加载的设置"""
        return self.settings

    def save_chat_summary_settings(self, chat_summary_settings):
        """保存聊天总结设置"""
        # 更新设置对象
        self.settings.update(chat_summary_settings)
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("聊天总结设置已保存")
        
    def save_settings(self, settings):
        """保存通用设置"""
        # 更新设置对象
        self.settings.update(settings)
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("设置已保存")
        
    def generate_chat_summary(self, contact_name):
        """生成聊天总结"""
        from tkinter import ttk  # 确保在方法内部导入ttk
        
        self.log(f"正在为联系人 {contact_name} 生成聊天总结...")
        
        # 获取聊天记录
        conversation = self.conversations.get(contact_name, [])
        if not conversation:
            self.log(f"找不到联系人 {contact_name} 的聊天记录")
            return
        
        # 获取聊天总结提示词
        chat_summary_prompt = self.settings.get('chat_summary_prompt06151', '')
        if not chat_summary_prompt:
            self.log("聊天总结提示词为空，使用默认提示词")
            chat_summary_prompt = "你是一个聊天总结专家，有着丰富的识别问题和提炼要点的能力。\n一下是上次总结的结果{last_summary}\n请将上一次的总结内容和以下与特定联系人的聊天对话新增内容进行合并总结：\n{chat_content}\n\n总结输出格式：\n1.联系人的主要话题和关注点\n2.联系人的语气和情绪\n3.对话中的重要信息和决定\n4.整体总结。"
        

        # 创建进度弹窗
        progress_window = tk.Toplevel(self.root)
        progress_window.title("聊天总结进度")
        progress_window.geometry("400x200")
        progress_window.resizable(False, False)
        
        # 窗口居中
        window_width = 400
        window_height = 200
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 添加标题
        title_label = ttk.Label(progress_window, text=f"正在为 {contact_name} 生成聊天总结...", font=('微软雅黑', 12, 'bold'))
        title_label.pack(pady=15)
        
        # 添加进度条
        progress = ttk.Progressbar(progress_window, orient="horizontal", length=350, mode="indeterminate")
        progress.pack(pady=15)
        progress.start()
        
        # 添加状态标签
        status_label = ttk.Label(progress_window, text="正在处理聊天记录...", font=('微软雅黑', 10))
        status_label.pack(pady=10)
        
        # 创建AI聊天对象
        api_settings = {
            'api_key': self.settings.get('api_key', ''),
            'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
            'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
            'provider': self.settings.get('provider', 'openai'),
            'bot_id': self.settings.get('bot_id', ''),
            'token': self.settings.get('token', ''),
            'system_prompt': "你是一个聊天总结专家",
            'delay': 0.1,
            'debug_mode': self.debug_mode
        }
        
        # 创建AI聊天对象并设置给处理器
        ai_chat = AiChat(api_settings, stop_flag=lambda: self.stop_requested06141)
        self.chat_summary_processor06151.set_ai_chat_module(ai_chat)
        
        # 生成总结
        def on_summary_complete(summary_item):
            if summary_item:
                if "error" in summary_item:
                    # 显示错误信息
                    error_message = summary_item["error"]
                    self.log(f"联系人 {contact_name} 的聊天总结生成失败: {error_message}")
                    # 更新状态和进度条
                    status_label.config(text=f"总结生成失败: {error_message}")
                    progress.stop()
                    
                    # 添加关闭按钮
                    close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                    close_btn.pack(pady=10)
                else:
                    self.log(f"联系人 {contact_name} 的聊天总结生成成功")
                    # 更新状态和进度条
                    status_label.config(text="总结已完成!")
                    progress.stop()
                    progress.config(mode="determinate", value=100)
                    
                    # 添加关闭按钮
                    close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                    close_btn.pack(pady=10)
                    
                    # 重新加载聊天总结数据
                    if hasattr(self, 'settings_ui') and self.settings_ui and hasattr(self.settings_ui, 'chat_summary_ui06151') and self.settings_ui.chat_summary_ui06151:
                        self.settings_ui.chat_summary_ui06151.load_summary_data()
                        self.log("已重新加载聊天总结数据")
            else:
                self.log(f"联系人 {contact_name} 的聊天总结生成失败")
                # 更新状态和进度条
                status_label.config(text="总结生成失败!")
                progress.stop()
                
                # 添加关闭按钮
                close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                close_btn.pack(pady=10)
        
        # 使用线程避免阻塞主线程
        import threading
        summary_thread = threading.Thread(
            target=lambda: self.chat_summary_processor06151.generate_summary(
                contact_name, 
                conversation, 
                chat_summary_prompt, 
                on_summary_complete
            )
        )
        summary_thread.daemon = True
        summary_thread.start()

    def reply_to_message(self, message_text):
        """对消息进行回复"""
        try:
            api_settings = {
                'api_key': self.settings.get('api_key', ''),
                'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
                'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
                'provider': self.settings.get('provider', 'openai'),
                'bot_id': self.settings.get('bot_id', ''),
                'token': self.settings.get('token', ''),
                'delay': self.settings.get('delay', 0.1),
                'debug_mode': self.debug_mode
            }
            
            # 创建AI聊天对象
            ai_chat = AiChat(api_settings, stop_flag=lambda: self.stop_requested06141)
            
            # 设置给聊天总结处理器
            self.chat_summary_processor06151.set_ai_chat_module(ai_chat)
            
            # 获取过滤后的会话历史（只包含最后总结之后的消息）
            if self.current_contact:
                filtered_history = self.get_filtered_conversation_history(self.current_contact)
            else:
                filtered_history = self.conversation_history
                self.debug_log("当前没有联系人，使用完整会话历史")
            
            # 生成回复
            self.log(f"正在生成回复...")
            reply = ai_chat.generate_reply(filtered_history)
            self.log(f"回复生成成功，正在输入...")
            
            # 输入回复
            ai_chat.type_reply(reply, self.input_area)
            
            # 保存回复到对话历史
            assistant_message = {"role": "assistant", "content": reply, "timestamp": int(time.time())}
            self.conversation_history.append(assistant_message)
            
            # 如果指定了联系人名称，保存到对应的会话中
            if self.current_contact:
                self.conversations[self.current_contact] = self.conversation_history
                self.storage_manager.save_conversation(self.current_contact, self.conversation_history)
                self.log(f"用户回复已保存到 {self.current_contact} 的会话历史中")
                self.debug_log(f"当前 {self.current_contact} 的会话历史记录数: {len(self.conversation_history)}")
            
            # 标记最近的用户消息为已回复
            for i in range(len(self.conversation_history) - 2, -1, -1):
                msg = self.conversation_history[i]
                if msg["role"] == "user" and not msg.get("is_replied", False):
                    msg["is_replied"] = True
                    break
            
            # 清空当前截屏的消息列表，再次获取消息
            time.sleep(1)  # 等待一秒再次获取消息，避免重复检测
            self.last_messages = []
            
            return True
        except Exception as e:
            self.log(f"回复消息时出错: {str(e)}")
            return False

def main():
    """主函数"""
    root = tk.Tk()
    app = ScreenChatBot(root)
    app.setup_ui()
    root.mainloop()

if __name__ == "__main__":
    if not check_lisence():
        sys.exit(0)
    main() 
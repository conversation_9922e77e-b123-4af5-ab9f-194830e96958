import json
import sys
import tkinter as tk
import queue
import time
import os
import threading
import cv2
import numpy as np
from PIL import Image, ImageTk
import markdown
import platform
import re
from tkinter import ttk, messagebox
from pynput.mouse import <PERSON><PERSON>, Controller
from utils.voice_read_detector import find_red_dot_with_nms
import pyautogui

# 导入自定义模块
from gui.ui_main import MainUI
from gui.settings_ui import SettingsUI
from gui.selection_ui import SelectionUI
from gui.history_ui import HistoryUI
from monitor.area_monitor import AreaMonitor
from ocr.message_extractor import MessageExtractor
from utils.storage import StorageManager
from utils.ai_chat import AiChat
from utils.image_processor import capture_screen_area
from utils.system_tools import check_lisence
from utils.chat_summary_processor06151 import ChatSummaryProcessor06151
from utils.voice_read_detector import find_red_dot_with_nms
from utils.mouse_takeover_detector06191 import MouseTakeoverDetector06191  # 导入鼠标接管检测器
from utils.active_dialogue_processor06242 import ActiveDialogueProcessor06242  # 导入主动对话处理器

class ScreenChatBot:
    def __init__(self, root):
        """
        应用程序主控制器
        :param root: Tkinter根窗口
        """
        self.root = root
        
        # 创建UI回调函数字典
        self.callbacks = {
            # ...回调函数字典
        }
        
        # 初始化UI
        self.ui = MainUI(root, self.callbacks)
        self.settings_ui = None
        
        # 初始化状态变量
        self.selection_done = False
        self.monitoring = False
        self.is_replying = False
        self.debug_mode = False
        self.reply_start_time = 0
        self.reply_timeout = 300
        self.contact_switch_triggered = False  # 联系人切换触发标志
        self.contact_switching = False         # 正在切换联系人标志
        self.stop_requested06141 = False  # 确保正确初始化为False
        self.is_exec_chat_area = False    # 是否执行聊天区域处理
        self.processed_message_hashes = set()  # 已处理消息哈希的集合，防止重复回复
        
        # 聊天总结模块 (新增)
        self.chat_summary_processor06151 = ChatSummaryProcessor06151()
        
        # 鼠标接管检测器 (新增)
        self.mouse_takeover_detector06191 = MouseTakeoverDetector06191(self)
        
        # 主动对话处理器 (新增)
        self.active_dialogue_processor06242 = ActiveDialogueProcessor06242(self)
        
        # 区域坐标
        # ...其他初始化代码

def main2_send_summary_email(_self, report_content, date_str):
    """发送总结邮件"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        from email.header import Header
        
        # 获取邮件设置
        mail_host = _self.settings.get('mail_host', '')
        mail_user = _self.settings.get('mail_user', '')
        mail_pass = _self.settings.get('mail_pass', '')
        receivers_str = _self.settings.get('receivers', '')
        
        if not mail_host or not mail_user or not mail_pass or not receivers_str:
            _self.log("邮件配置不完整，无法发送")
            return
            
        # 解析收件人列表
        receivers = [email.strip() for email in receivers_str.split(',') if email.strip()]
        
        if not receivers:
            _self.log("未指定收件人，无法发送邮件")
            return
        
        # 预处理Markdown内容，直接将数字+点格式的内容转换为HTML格式
        # 这样可以保持原有的编号而不会被Markdown解析器误解为列表
        lines = report_content.split('\n')
        processed_lines = []
        
        for line in lines:
            # 检查行是否以数字和点开头 (如 "1."、"2.", 等)
            match = re.match(r'^(\d+)\.\s*(.*)', line.strip())
            if match:
                # 保留原格式但不使用Markdown的有序列表
                num, text = match.groups()
                processed_lines.append(f"{num}. {text}")
            else:
                processed_lines.append(line)
        
        processed_content = '\n'.join(processed_lines)
        
        # 不使用标准Markdown解析，而是直接制作HTML
        html_parts = []
        in_paragraph = False
        
        for line in processed_lines:
            line = line.strip()
            
            # 标题处理
            if line.startswith('# '):
                if in_paragraph:
                    html_parts.append('</p>')
                    in_paragraph = False
                html_parts.append(f'<h1>{line[2:]}</h1>')
            elif line.startswith('## '):
                if in_paragraph:
                    html_parts.append('</p>')
                    in_paragraph = False
                html_parts.append(f'<h2>{line[3:]}</h2>')
            # 数字+点处理
            elif re.match(r'^\d+\.\s+', line):
                match = re.match(r'^(\d+)\.\s+(.*)', line)
                num, text = match.groups()
                if in_paragraph:
                    html_parts.append('</p>')
                    in_paragraph = False
                html_parts.append(f'<p><strong>{num}.</strong> {text}</p>')
            # 空行处理
            elif not line:
                if in_paragraph:
                    html_parts.append('</p>')
                    in_paragraph = False
                html_parts.append('<br/>')
            # 普通文本处理
            else:
                if not in_paragraph:
                    html_parts.append('<p>')
                    in_paragraph = True
                else:
                    html_parts.append('<br/>')
                html_parts.append(line)
        
        if in_paragraph:
            html_parts.append('</p>')
        
        html_content = '\n'.join(html_parts)
        
        # HTML邮件模板
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>微信聊天日报 - {date_str}</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 900px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                h1 {{
                    color: #2b579a;
                    border-bottom: 2px solid #2b579a;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }}
                h2 {{
                    color: #5c7db0;
                    margin-top: 30px;
                    padding-bottom: 5px;
                    border-bottom: 1px solid #ddd;
                }}
                ol, ul {{
                    margin-left: 20px;
                    padding-left: 20px;
                }}
                li {{
                    margin: 8px 0;
                }}
                p {{
                    margin: 10px 0;
                }}
                .summary-container {{
                    background-color: #f9f9f9;
                    border-left: 4px solid #2b579a;
                    padding: 15px;
                    margin: 20px 0;
                }}
                .footer {{
                    margin-top: 40px;
                    padding-top: 10px;
                    border-top: 1px solid #ddd;
                    font-size: 0.9em;
                    color: #777;
                    text-align: center;
                }}
            </style>
        </head>
        <body>
            <div class="content">
                {html_content}
            </div>
            <div class="footer">
                <p>聊天助手自动生成 - {date_str}</p>
            </div>
        </body>
        </html>
        """
            
        # 创建一个带有附件的邮件实例
        message = MIMEMultipart()
        message['From'] = Header(f"{mail_user}", 'utf-8')
        message['To'] = Header(f"{', '.join(receivers)}", 'utf-8')
        message['Subject'] = Header(f"聊天日报 - {date_str}", 'utf-8')
        
        # 添加HTML内容
        message.attach(MIMEText(html_template, 'html', 'utf-8'))
        
        # 发送邮件
        try:
            smtp_obj = smtplib.SMTP(mail_host)
            smtp_obj.login(mail_user, mail_pass)
            smtp_obj.sendmail(mail_user, receivers, message.as_string())
            _self.log("邮件发送成功")
        except smtplib.SMTPException as e:
            _self.log(f"邮件发送失败: {str(e)}")
            
    except Exception as e:
        _self.log(f"发送邮件时出错: {str(e)}")
        import traceback
        _self.log(f"错误详情: {traceback.format_exc()}")

def main2_get_system_prompt_for_contact(_self, contact_name):
    """根据联系人名称获取系统提示词"""
    _self.debug_log(f"开始为联系人'{contact_name}'匹配系统提示词")
    
    # 如果设置界面已初始化，使用其方法获取匹配的系统提示词
    if hasattr(_self, 'settings_ui') and _self.settings_ui:
        _self.debug_log("通过settings_ui获取系统提示词")
        prompt = _self.settings_ui.get_system_prompt_for_contact(contact_name)
        # 确保prompt不为None
        if prompt is None:
            prompt = ""
            _self.debug_log("警告：从settings_ui获取到的prompt为None，已设置为空字符串")
        _self.debug_log(f"从settings_ui获取到提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
        return prompt
        
    # 否则，自行进行匹配
    prompt_templates = _self.settings.get('prompt_templates', [])
    _self.debug_log(f"从设置中获取提示词模板，共{len(prompt_templates)}个")
    
    # 如果联系人名称存在，尝试匹配关键词
    if contact_name:
        for template in prompt_templates:
            keyword = template.get('keyword', '')
            if keyword and keyword in contact_name:
                prompt = template.get('prompt', '')  # 添加默认空字符串
                if prompt is None:
                    prompt = ""
                _self.debug_log(f"联系人'{contact_name}'匹配到关键词'{keyword}'，使用对应的提示词: {prompt[:30]}..." if prompt and len(prompt) > 30 else prompt)
                return prompt
            else:
                _self.debug_log(f"关键词'{keyword}'与联系人'{contact_name}'不匹配")
                
    # 没有匹配到关键词，使用默认系统提示词
    default_prompt = _self.settings.get('default_system_prompt', '你是一个有用的助手。请用简短、友好的方式回答问题。')
    # 确保default_prompt不为None
    if default_prompt is None:
        default_prompt = '你是一个有用的助手。请用简短、友好的方式回答问题。'
    
    _self.debug_log(f"未匹配到关键词，使用默认提示词: {default_prompt[:30]}..." if default_prompt and len(default_prompt) > 30 else default_prompt)
    return default_prompt

def main2_save_conversation(_self, contact_name, conversation_data):
    """保存指定联系人的对话历史"""
    result = _self.storage_manager.save_conversation(contact_name, conversation_data)
    if result:
        _self.debug_log(f"已保存'{contact_name}'的聊天历史")
    return result
    
def main2_switch_contact(_self, new_contact_name):
    """切换当前联系人"""
    if not new_contact_name:
        _self.debug_log("未提供新联系人名称，无法切换")
        return
        
    # 如果当前有联系人，保存当前会话
    if _self.current_contact:
        # 只有在不是None的情况下才保存，以避免保存空会话
        if _self.conversation_history:
            _self.save_conversation(_self.current_contact, _self.conversation_history)
            _self.debug_log(f"已保存当前会话'{_self.current_contact}'的状态，共{len(_self.conversation_history)}条消息")
    
    # 记录旧联系人名称
    old_contact = _self.current_contact
    
    # 切换到新联系人
    _self.current_contact = new_contact_name
    
    # 重置消息跟踪集合，确保新联系人不受之前联系人消息的干扰
    _self.processed_message_hashes = set()
    _self.debug_log(f"切换联系人，已重置消息跟踪集合")
    
    # 检查联系人是否在不理睬名单中
    if new_contact_name in _self.settings.get('ignored_contacts', []):
        _self.log(f"【提示】联系人 '{new_contact_name}' 在特殊联系人名单中，将特殊处理此联系人的消息，是否回复将取决于您的配置")
    
    # 加载新联系人的聊天历史
    _self.conversation_history = _self.conversations.get(new_contact_name, [])
    
    # 确保历史会话中的消息回复状态正确
    if _self.conversation_history:
        last_msg_role = None
        for i, msg in enumerate(_self.conversation_history):
            # 确保用户消息后面跟着的助手消息将用户消息标记为已回复
            if msg["role"] == "user" and not msg.get("is_replied", False):
                # 检查后面是否有助手回复
                if i + 1 < len(_self.conversation_history) and _self.conversation_history[i + 1]["role"] == "assistant":
                    msg["is_replied"] = True
                    _self.debug_log(f"在切换联系人时将用户消息标记为已回复: {msg['content']}")
            
            last_msg_role = msg["role"]
    
    # 重置last_messages，以便下次检测能正确识别所有消息
    _self.last_messages = []
    _self.last_message_count = 0
    
    # 更新消息计数
    _self.contact_message_counts[new_contact_name] = len(_self.conversation_history)
    
    _self.log(f"已切换到联系人'{new_contact_name}'，{'加载了'+str(len(_self.conversation_history))+'条历史消息' if _self.conversation_history else '没有历史消息'}")
    
    # 返回切换结果和旧联系人名称
    return True, old_contact

def main2_process_chat_area(_self):
    """处理聊天区域，识别消息并处理回复"""
    # 如果正在切换联系人，则跳过聊天区域处理
    if _self.contact_switching:
        # _self.is_exec_chat_area = False
        _self.debug_log("正在切换联系人，暂停聊天区域处理")
        return
        
    # 检查是否已请求停止监控 - 将检查移到方法开始处
    if _self.stop_requested06141:
        _self.is_exec_chat_area = False
        _self.debug_log("检测到停止请求，跳过聊天区域处理")
        return
    
    # 检查当前联系人是否在不理睬名单中
    if _self.current_contact:
        should_ignore = False
        if hasattr(_self, 'settings_ui'):
            # 使用settings_ui对象的方法来判断
            should_ignore = _self.settings_ui.check_contact_should_be_ignored(_self.current_contact)
        else:
            # 旧的逻辑作为后备
            ignored_contacts = _self.settings.get('ignored_contacts', [])
            for ignored_contact in ignored_contacts:
                if ignored_contact in _self.current_contact:
                    should_ignore = True
                    break
        
        if should_ignore:
            _self.debug_log(f"联系人 '{_self.current_contact}' 在不理睬名单中，跳过聊天区域处理")
            _self.is_exec_chat_area = False
            return
    
    # 确保全局变量已初始化
    if not hasattr(_self, 'processed_message_hashes'):
        _self.processed_message_hashes = set()
        _self.debug_log("初始化消息跟踪集合")
    
    # 设置递归检查标志，防止过深递归
    recursion_depth = getattr(_self, 'chat_area_recursion_depth', 0)
    _self.chat_area_recursion_depth = recursion_depth + 1
    
    # 限制最大递归深度，防止无限循环
    if recursion_depth > 5:
        _self.debug_log(f"警告：聊天区域处理递归深度已达 {recursion_depth}，防止无限循环")
        _self.is_exec_chat_area = False
        _self.chat_area_recursion_depth = 0
        return
        
    try:
        _self.is_exec_chat_area = True
        # 截取屏幕区域
        x1, y1, x2, y2 = _self.chat_area
        screenshot = capture_screen_area(x1, y1, x2, y2, _self.noise_areas)
        _self.debug_log(f"截图完成，尺寸: {screenshot.shape[1]}x{screenshot.shape[0]}")
        
        # 保存当前截图用于调试
        if _self.debug_mode:
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            debug_img_path = os.path.join(debug_dir, f"screenshot_{timestamp}.png")
            cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            _self.debug_log(f"已保存调试截图: {debug_img_path}")
        
        # 语音消息红点检测和处理逻辑06181
        from utils.voice_read_detector import find_red_dot_with_nms
        import pyautogui
        
        # 保存截图用于检测
        debug_dir = "debug_screenshots"
        os.makedirs(debug_dir, exist_ok=True)
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        debug_img_path = os.path.join(debug_dir, f"voice_detection_{timestamp}.png")
        cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
        
        _self.debug_log("开始检测语音消息红点...")
        voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
        
        if voice_red_dots06181:
            _self.debug_log(f"找到 {len(voice_red_dots06181)} 个语音消息红点")
            
            # 获取截图尺寸
            img_height06181, img_width06181 = screenshot.shape[:2]
            _self.debug_log(f"截图尺寸: 宽={img_width06181}, 高={img_height06181}")
            
            # 获取聊天区域尺寸
            chat_width06181 = x2 - x1
            chat_height06181 = y2 - y1
            _self.debug_log(f"聊天区域尺寸: 宽={chat_width06181}, 高={chat_height06181}")
            
            # 计算缩放比例 (如果截图尺寸与聊天区域不一致)
            scale_x06181 = chat_width06181 / img_width06181
            scale_y06181 = chat_height06181 / img_height06181
            _self.debug_log(f"缩放比例: x={scale_x06181}, y={scale_y06181}")
            
            # 处理所有语音红点直到没有为止
            processed_count06181 = 0
            max_attempts06181 = 5  # 防止无限循环
            
            while voice_red_dots06181 and processed_count06181 < max_attempts06181:
                # 检查是否已请求停止监控
                if _self.stop_requested06141:
                    _self.debug_log("检测到停止请求，中断语音消息红点处理")
                    break
                    
                # 获取第一个红点的坐标
                dot_x06181, dot_y06181 = voice_red_dots06181[0]
                _self.debug_log(f"处理语音红点#{processed_count06181+1}，截图中的相对坐标: ({dot_x06181}, {dot_y06181})")
                
                # 应用缩放比例
                scaled_x06181 = int(dot_x06181 * scale_x06181)
                scaled_y06181 = int(dot_y06181 * scale_y06181)
                _self.debug_log(f"缩放后坐标: ({scaled_x06181}, {scaled_y06181})")
                
                # 计算语音气泡位置 (红点左侧像素处)
                voice_bubble_offset = 100  # 默认值
                if hasattr(_self.ui, 'voice_bubble_offset_var'):
                    voice_bubble_offset = _self.ui.voice_bubble_offset_var.get()
                voice_bubble_x06181 = scaled_x06181 - int(voice_bubble_offset * scale_x06181)
                # 限制不要超出屏幕边界
                voice_bubble_x06181 = max(10, voice_bubble_x06181)
                
                # 转换为屏幕绝对坐标
                abs_x06181 = x1 + voice_bubble_x06181
                abs_y06181 = y1 + scaled_y06181
                
                # 记录详细的坐标信息用于调试
                _self.debug_log(f"聊天区域在屏幕上的位置: ({x1}, {y1}, {x2}, {y2})")
                _self.debug_log(f"点击语音消息，屏幕绝对坐标: ({abs_x06181}, {abs_y06181})")
                
                # 确保坐标在有效范围内
                if x1 <= abs_x06181 <= x2 and y1 <= abs_y06181 <= y2:
                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(_self.ui, 'mouse_interval_var'):
                        mouse_interval = _self.ui.mouse_interval_var.get()
                    
                    # 移动鼠标并点击
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    pyautogui.moveTo(abs_x06181, abs_y06181)
                    mouse.position = (abs_x06181, abs_y06181)
                    if platform.system() == "Darwin":
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                    else:
                        mouse.press(Button.right)
                        mouse.release(Button.right)
                    new_abs_x06181 = abs_x06181 + 15
                    new_abs_y06181 = abs_y06181 + 15
                    mouse.position = (new_abs_x06181, new_abs_y06181)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_abs_x06181, new_abs_y06181-50)

                else:
                    _self.debug_log(f"警告：计算出的坐标 ({abs_x06181}, {abs_y06181}) 超出了聊天区域范围，调整点击位置")
                    # 调整到区域内的安全位置
                    safe_x = min(max(abs_x06181, x1 + 10), x2 - 10)
                    safe_y = min(max(abs_y06181, y1 + 10), y2 - 10)
                    _self.debug_log(f"调整后的安全坐标: ({safe_x}, {safe_y})")

                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(_self.ui, 'mouse_interval_var'):
                        mouse_interval = _self.ui.mouse_interval_var.get()
                        
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    pyautogui.moveTo(safe_x, safe_y)
                    mouse.position = (safe_x, safe_y)
                    if platform.system() == "Darwin":
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                    else:
                        mouse.press(Button.right)
                        mouse.release(Button.right)
                    new_safe_x = safe_x + 15
                    new_safe_y = safe_y + 15
                    mouse.position = (new_safe_x, new_safe_y)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_safe_x, new_safe_y-50)

                # 等待语音消息处理完成
                # 获取等待转文字时间
                voice_wait = 6.0  # 默认值
                if hasattr(_self.ui, 'voice_wait_var'):
                    voice_wait = _self.ui.voice_wait_var.get()
                _self.debug_log(f"等待语音转文字完成，等待时间: {voice_wait}秒")
                
                # 分段等待，每次检查是否有停止请求
                wait_segments = 10  # 将等待时间分为10段
                segment_time = voice_wait / wait_segments
                for _ in range(wait_segments):
                    # 检查是否已请求停止监控
                    if _self.stop_requested06141:
                        _self.debug_log("检测到停止请求，中断语音消息等待")
                        break
                    time.sleep(segment_time)
                
                # 如果已请求停止，跳出循环
                if _self.stop_requested06141:
                    _self.debug_log("检测到停止请求，中断语音消息红点处理")
                    break

                pyautogui.scroll(-500)
                
                processed_count06181 += 1
                
                
                # 再次截图并检测红点
                screenshot = capture_screen_area(x1, y1, x2, y2, _self.noise_areas)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_img_path = os.path.join(debug_dir, f"voice_detection_after_{timestamp}.png")
                cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                
                # 再次检测红点
                prev_count = len(voice_red_dots06181)
                voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
                curr_count = len(voice_red_dots06181)
                
                if voice_red_dots06181:
                    _self.debug_log(f"仍有 {curr_count} 个语音消息红点待处理 (之前有 {prev_count} 个)")
                    
                    # 如果红点数量没有减少，尝试其他策略
                    if curr_count >= prev_count and processed_count06181 > 1:
                        _self.debug_log(f"警告：多次处理后红点数量未减少，请先测试语音消息红点处理")

                else:
                    _self.debug_log(f"所有语音消息已处理完成，共处理 {processed_count06181} 个语音红点")
            
            if processed_count06181 >= max_attempts06181 and voice_red_dots06181:
                _self.debug_log(f"达到最大尝试次数 ({max_attempts06181})，终止语音红点处理")
        else:
            _self.debug_log("未检测到语音消息红点")
        
        # 检查是否已请求停止监控
        if _self.stop_requested06141:
            _self.debug_log("检测到停止请求，跳过OCR处理")
            _self.chat_area_recursion_depth = max(0, recursion_depth)  # 恢复递归计数
            _self.is_exec_chat_area = False
            return
            
        # 构建OCR参数
        ocr_params = {
            'debug_mode': _self.debug_mode,
            'mask_green_bubbles': _self.ui.mask_green_var.get(),
            'current_contact': _self.current_contact,
            'screen_center_x': _self.screen_center_x,
            'x1': x1,
            'y1': y1,
            'y2': y2,
            'center_margin': _self.ui.center_margin_var.get(),
            'line_spacing_threshold': _self.ui.line_spacing_var.get(),
            'conversation_history': _self.conversation_history,
            'green_r': _self.ui.green_r_var.get(),
            'green_g': _self.ui.green_g_var.get(),
            'green_b': _self.ui.green_b_var.get(),
            'green_tol': _self.ui.green_tol_var.get(),
            'green_min_area': _self.ui.green_min_area_var.get(),
            'text_color_enabled': _self.ui.text_color_enabled_var.get(),
            'text_color_r': _self.ui.text_color_r_var.get(),
            'text_color_g': _self.ui.text_color_g_var.get(),
            'text_color_b': _self.ui.text_color_b_var.get(),
            'text_color_tol': _self.ui.text_color_tol_var.get()
        }
        
        # OCR识别文本
        _self.debug_log("开始OCR识别...")
        start_time = time.time()
        messages, contact_name = _self.message_extractor.extract_messages(screenshot, ocr_params)
        ocr_time = time.time() - start_time
        
        _self.debug_log(f"OCR识别完成，耗时: {ocr_time:.2f}秒")
        _self.debug_log(f"OCR结果: 识别到{len(messages)}条文字")
        
        # 如果没有识别到消息，检查是否需要重试
        if not messages:
            _self.debug_log("未识别到任何消息，可能需要调整OCR参数或截图区域")
            # 记录详细日志便于调试
            h, w = screenshot.shape[:2]
            _self.debug_log(f"截图尺寸: {w}x{h}，聊天区域: {_self.chat_area}")
            _self.chat_area_recursion_depth = max(0, recursion_depth)  # 恢复递归计数
            _self.is_exec_chat_area = False
            return
        
        # 检查是否需要更新联系人名称 - 只有在明确触发的情况下才更新
        # 联系人切换标志由monitor_contact_area在点击红点后设置
        if _self.contact_switch_triggered and contact_name:
            _self.debug_log(f"检测到联系人切换标志已触发，且OCR识别到联系人名称: {contact_name}")
            # 如果联系人名称发生变化，则切换联系人
            if _self.current_contact != contact_name:
                _self.debug_log(f"联系人名称变更: '{_self.current_contact}' -> '{contact_name}'")
                _self.switch_contact(contact_name)
            # 重置联系人切换标志
            _self.contact_switch_triggered = False
        
        # 处理新消息，发送回复
        _self.detect_and_reply_to_messages(messages)
        
    except Exception as e:
        _self.log(f"处理聊天区域时出错: {str(e)}")
        import traceback
        _self.debug_log(traceback.format_exc())
        
    # 检查处理结果，判断是否检测到新消息
    if not getattr(_self, 'is_replying', False) and len(messages) == _self.last_message_count:
        # 没有检测到新消息，可以设置标志为False
        _self.debug_log(f"检测完成，没有新消息需要处理，聊天区处理可以暂停，允许联系人区域检测")
        # 恢复递归深度计数
        _self.chat_area_recursion_depth = max(0, _self.chat_area_recursion_depth)
        _self.is_exec_chat_area = False
    else:
        # 还有新消息或正在回复中，保持聊天区处理活跃
        _self.debug_log(f"检测到新消息或正在回复中，保持聊天区处理活跃状态")
        _self.is_exec_chat_area = True
    

def main2_get_filtered_conversation_history(_self, contact_name):
    """
    根据最后总结时间戳过滤会话历史，只返回最后总结之后的消息
    如果没有总结记录，则返回完整历史
    """
    # 获取最后的总结记录
    last_summary = _self.chat_summary_processor06151.get_last_summary(contact_name)
    
    if not last_summary:
        _self.debug_log(f"联系人 {contact_name} 没有总结记录，使用完整会话历史")
        return _self.conversation_history
    
    # 获取最后总结时间戳
    last_summary_time = last_summary.get("last_time_summary", 0)
    _self.debug_log(f"联系人 {contact_name} 最后总结时间戳: {last_summary_time}")
    
    # 过滤出最后总结之后的消息
    filtered_history = []
    
    # 添加最后总结之后的消息
    for msg in _self.conversation_history:
        if msg.get("timestamp", 0) > last_summary_time:
            filtered_history.append(msg)
    
    print(f"过滤后的会话历史共 {len(filtered_history)} 条消息，原有 {len(_self.conversation_history)} 条")
    return filtered_history

def main2_detect_and_reply_to_messages(_self, messages):
    """检测新消息并处理回复"""
    try:
        # 如果已经请求停止监控，不再检测新消息
        if getattr(_self, 'stop_requested06141', False):
            _self.debug_log("检测到停止请求，跳过新消息检测")
            return
            
        # 检查当前联系人是否在不理睬名单中（使用包含匹配）
        should_ignore = False
        if hasattr(_self, 'settings_ui'):
            # 使用settings_ui对象的方法来判断
            should_ignore = _self.settings_ui.check_contact_should_be_ignored(_self.current_contact)
            if should_ignore:
                _self.debug_log(f"联系人 '{_self.current_contact}' 在不理睬名单中，跳过回复")
        else:
            # 旧的逻辑作为后备
            ignored_contacts = _self.settings.get('ignored_contacts', [])
            for ignored_contact in ignored_contacts:
                if ignored_contact in _self.current_contact:
                    should_ignore = True
                    _self.debug_log(f"联系人 '{_self.current_contact}' 匹配不理睬规则 '{ignored_contact}'，跳过回复")
                    break
            
        if should_ignore:
            _self.debug_log(f"联系人 '{_self.current_contact}' 在不理睬名单中，跳过回复")
            return
            
        # 检测新消息
        _self.debug_log(f"开始检测新消息（当前历史消息数量：{_self.last_message_count}）")
        new_messages = _self.detect_new_messages(messages)
        
        if new_messages:
            _self.log(f"【重要】检测到{len(new_messages)}条新的未回复消息！")
            
            # 添加新消息到历史记录（但只添加用户消息，因为new_messages现在只包含用户消息）
            for msg in new_messages:
                role = msg["role"]
                content = msg["content"]
                role_text = "用户" if role == "user" else "助手"
                _self.log(f"【新消息】[{_self.current_contact}-{role_text}]: {content}")
                # 将新消息添加到对话历史
                _self.conversation_history.append(msg)
            
            # 判断是否有未回复的用户消息
            if new_messages:
                # 检查用户消息是否触发固定回复规则
                user_message_triggered = False
                for msg in new_messages:
                    if msg["role"] == "user":
                        if _self.fixed_reply_processor.process_user_message(msg["content"]):
                            _self.log(f"【固定回复】用户消息触发固定回复规则: {msg['content'][:50]}...")
                            # 标记消息为已回复
                            msg["is_replied"] = True
                            user_message_triggered = True
                            break

                # 如果触发了固定回复规则，跳过AI回复
                if user_message_triggered:
                    _self.log("【固定回复】已执行固定回复，跳过AI回复")
                    # 标记所有新消息为已回复
                    for msg in new_messages:
                        msg["is_replied"] = True
                    _self.log(f"已将{len(new_messages)}条用户消息标记为已回复")
                else:
                    # 有未回复的用户消息，需要生成回复
                    _self.log(f"【需要回复】检测到{len(new_messages)}条未回复的用户消息，准备生成回复")

                    # 设置回复标志
                    _self.is_replying = True
                    _self.reply_start_time = time.time()

                    # 重置停止请求标志
                    _self.stop_requested06141 = False

                    # 获取针对当前联系人的系统提示词
                    system_prompt = _self.get_system_prompt_for_contact(_self.current_contact)
                    # 确保system_prompt不为None，如果是None则使用空字符串
                    if system_prompt is None:
                        system_prompt = ""
                        _self.debug_log("警告：获取到的system_prompt为None，已设置为空字符串")

                    system_prompt = system_prompt.replace("{联系人名称}", _self.current_contact)

                    # 获取之前对话总结
                    summary_file = f"summary_chat/{_self.current_contact}.json"
                    if os.path.exists(summary_file):
                        with open(summary_file, 'r', encoding='utf-8') as f:
                            summary_data = json.load(f)
                            summary_text = summary_data[-1]["summary_content"]
                    else:
                        summary_text = ''

                    system_prompt = system_prompt.replace("{之前对话总结}", summary_text)

                    print("系统提示词：",system_prompt)

                    _self.debug_log(f"联系人'{_self.current_contact}'使用系统提示词: {system_prompt[:50]}..." if system_prompt and len(system_prompt) > 50 else system_prompt)

                    # 创建AI聊天实例
                    api_settings = {
                        'provider': _self.settings.get('provider', 'openai'),
                        'api_key': _self.settings.get('api_key', ''),
                        'api_base_url': _self.settings.get('api_base_url', 'https://api.openai.com'),
                        'model_name': _self.settings.get('model_name', 'gpt-3.5-turbo'),
                        'bot_id': _self.settings.get('bot_id', ''),
                        'token': _self.settings.get('token', ''),
                        'system_prompt': system_prompt,
                        'delay': _self.ui.delay_var.get(),
                        'debug_mode': _self.debug_mode
                    }
                    _self.debug_log(f"测试回复使用API提供商: {api_settings['provider']}")
                    ai_chat = AiChat(api_settings, stop_flag=lambda: _self.stop_requested06141)

                    # 获取过滤后的会话历史（只包含最后总结之后的消息）
                    filtered_history = _self.get_filtered_conversation_history(_self.current_contact)

                    # 生成回复
                    reply_list = ai_chat.generate_reply(filtered_history, _self.input_area)
                    print(reply_list)
                    reply = reply_list[-1]["content"]
                    # 模拟输入回复
                    ai_chat.type_reply(reply, _self.input_area, _self)


                    _self.conversation_history += reply_list
                    _self.log(f"【助手回复】已添加到对话历史: {reply[:50]}..." if len(reply) > 50 else reply)

                    # 检查AI回复是否触发固定回复规则
                    _self.fixed_reply_processor.process_ai_reply(reply)

                    # 重要：标记所有新消息为已回复
                    for msg in new_messages:
                        msg["is_replied"] = True
                    _self.log(f"已将{len(new_messages)}条用户消息标记为已回复")

                    # 在原始messages列表中也标记这些消息为已回复
                    for msg in messages:
                        if msg["role"] == "user" and not msg.get("is_replied", False):
                            # 检查这条消息是否在new_messages中
                            for new_msg in new_messages:
                                if msg["content"] == new_msg["content"]:
                                    msg["is_replied"] = True
                                    _self.debug_log(f"在原始消息列表中将消息标记为已回复: {msg['content']}")
                                    break

                    # 清除回复标志
                    _self.is_replying = False
            else:
                _self.log(f"【无需回复】没有新的未回复用户消息，跳过回复")
            
            # 更新当前联系人的会话记录
            _self.conversations[_self.current_contact] = _self.conversation_history
            
            # 保存更新后的会话历史
            _self.save_conversation(_self.current_contact, _self.conversation_history)
        else:
            _self.debug_log("未检测到新的未回复消息")
        
        # 更新上一次消息记录
        _self.last_messages = messages
        _self.last_message_count = len(messages)
        
    except Exception as e:
        _self.log(f"检测新消息时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())

def main2_detect_new_messages(_self, current_messages):
    """检测新消息，比较当前消息和历史消息"""
    # 如果当前没有联系人，无法检测新消息
    if not _self.current_contact:
        _self.debug_log("当前没有活跃联系人，无法检测新消息")
        return []
        
    # 确保全局跟踪集合已初始化
    if not hasattr(_self, 'processed_message_hashes'):
        _self.processed_message_hashes = set()
        _self.debug_log("在detect_new_messages中初始化消息跟踪集合")
        
    # 是否刚刚发生了联系人切换（通过last_messages为空且conversation_history不为空判断）
    just_switched = not _self.last_messages and _self.conversation_history and len(_self.conversation_history) > 0
    if just_switched:
        _self.debug_log("检测到刚刚切换联系人，将重新比较全部消息")
        # 当联系人切换时，清空消息跟踪集合
        _self.processed_message_hashes = set()
        _self.debug_log("联系人切换，清空消息跟踪集合")
        
    # 比较当前消息和之前的消息，找出新消息
    if not _self.last_messages or just_switched or 1:
        _self.debug_log("首次检测或刚切换联系人，设置基准消息并检查是否有用户消息需要回复")
        
        # 找出当前消息中未在历史记录中的用户消息
        new_user_messages = []
        
        # 检查当前消息中是否有未在历史记录中出现的用户消息
        history_content = {msg["content"] for msg in _self.conversation_history}

        # 计算当前所有聊天记录和历史记录中用户消息最后两条消息的相似度
        from utils.sentence_similarity import sentence_similarity_by_edit_distance
        history_last_2_content_user_list = [msg for msg in _self.conversation_history if msg["role"] == "user"]
        if len(history_last_2_content_user_list) >= 2:
            history_last_2_content_user_list = history_last_2_content_user_list[-2:]
        else:
            history_last_2_content_user_list = history_last_2_content_user_list
        
        print("just_switched================================================")
        print(history_last_2_content_user_list)
        print("just_switched================================================")

        for msg in current_messages:
            if msg["role"] == "user" and not msg.get("is_replied", False) and msg["content"] not in history_content:
                # 创建消息哈希，用于全局跟踪
                msg_hash = hash(f"{_self.current_contact}:{msg['content']}")

                is_similar = False
                for h in history_last_2_content_user_list:
                    similarity = sentence_similarity_by_edit_distance(msg["content"],h["content"])
                    if similarity > 0.8:
                        _self.log(f"当前消息和历史记录中最后两条消息的相似度: {similarity}, 跳过")
                        is_similar = True
                        break
                
                if is_similar:
                    continue
                
                # 检查是否已处理过该消息

                if msg_hash not in _self.processed_message_hashes:
                    new_user_messages.append(msg)
                    # 添加到全局跟踪集合
                    _self.processed_message_hashes.add(msg_hash)
                    _self.log(f"找到未在历史记录中的用户消息: {msg['content']}")
                else:
                    _self.log(f"跳过已处理过的消息(hash): {msg['content']}")
        
        if new_user_messages:
            _self.log(f"首次检测到{len(new_user_messages)}条未回复的用户消息，将被处理")
            
            # 如果有多条新消息，只处理最后一条，避免一次性回复多条
            if len(new_user_messages) > 1:
                last_user_message = new_user_messages[-1]
                _self.debug_log(f"选择最后一条用户消息进行回复: {last_user_message['content']}")
                new_user_messages = [last_user_message]
            
            # 更新历史消息和计数
            _self.last_messages = current_messages
            _self.last_message_count = len(current_messages)
            
            # 返回用户消息，使其可以被回复
            return new_user_messages
        else:
            _self.debug_log("首次检测未发现未回复的用户消息")
            _self.last_messages = current_messages
            _self.last_message_count = len(current_messages)
            return []
    
    new_messages = []
    
    # 更复杂的检测方法：检查当前消息是否包含新的内容
    if len(current_messages) > _self.last_message_count:
        # 有新消息加入
        temp_new_messages = current_messages[_self.last_message_count:]
        _self.log(f"检测到新消息: 当前消息数({len(current_messages)}) > 历史消息数({_self.last_message_count})")
        
        # 过滤已经由AI生成的回复消息和已回复的用户消息，只保留未回复的用户消息
        for msg in temp_new_messages:
            # 只添加未回复的用户消息
            if msg["role"] == "user" and not msg.get("is_replied", False):
                # 创建消息哈希，用于全局跟踪
                msg_hash = hash(f"{_self.current_contact}:{msg['content']}")
                
                # 检查是否已经在全局跟踪集合中
                if msg_hash in _self.processed_message_hashes:
                    _self.debug_log(f"  跳过已处理过的消息(hash): {msg['content']}")
                    continue


                is_duplicate = False
                # 检查消息相似度与历史消息最后两条用户消息的相似度
                from utils.sentence_similarity import sentence_similarity_by_edit_distance
                history_last_2_content_user_list = [msg for msg in _self.conversation_history if msg["role"] == "user"]
                if len(history_last_2_content_user_list) >= 2:
                    history_last_2_content_user_list = history_last_2_content_user_list[-2:]
                else:
                    history_last_2_content_user_list = history_last_2_content_user_list
                
                print("================================================")
                print(history_last_2_content_user_list)
                print("================================================")

                for h in history_last_2_content_user_list:
                    similarity = sentence_similarity_by_edit_distance(msg["content"],h["content"])
                    if similarity > 0.8:
                        print(f"当前消息和历史记录中消息的相似度: {similarity}, 跳过")
                        msg["is_replied"] = True
                        is_duplicate = True
                        break
                
                # 检查是否已经在历史记录中
                for hist_msg in _self.conversation_history:

                    if hist_msg["content"] == msg["content"] and hist_msg["role"] == "user":
                        is_duplicate = True
                        # 如果历史记录中的消息已标记为已回复，则当前消息也应标记为已回复
                        if hist_msg.get("is_replied", False):
                            msg["is_replied"] = True
                            _self.debug_log(f"  将当前消息标记为已回复(与历史记录一致): {msg['content']}")
                        break
                
                if not is_duplicate:
                    # 未回复的用户消息添加到新消息列表
                    new_messages.append(msg)
                    # 添加到全局跟踪集合
                    _self.processed_message_hashes.add(msg_hash)
                    _self.debug_log(f"  添加未回复的用户新消息: {msg['content']}")
                else:
                    if not msg.get("is_replied", False):
                        _self.debug_log(f"  消息内容已存在于历史记录中，但标记为未回复: {msg['content']}")
                    else:
                        _self.debug_log(f"  跳过已存在于历史记录的用户消息: {msg['content']}")
            elif msg["role"] == "user" and msg.get("is_replied", False):
                # 跳过已回复的用户消息
                _self.debug_log(f"  跳过已回复的用户消息: {msg['content']}")
            elif msg["role"] == "assistant":
                # 对于助手消息，只记录，不添加到new_messages
                _self.debug_log(f"  跳过助手消息: {msg['content']}，助手消息只在生成回复时添加")
            elif msg["role"] == "tool":
                _self.debug_log(f"  跳过工具消息: {msg['content']}，工具消息只在生成回复时添加")
        
        _self.debug_log(f"联系人'{_self.current_contact}'的新增消息 (仅未回复用户消息): {len(new_messages)}")
        for i, msg in enumerate(new_messages):
            _self.debug_log(f"  新增{i+1}: {msg['content']} (角色: {msg['role']})")
    else:
        # 比较消息内容
        _self.debug_log("消息数量无变化，比较内容差异")
        
        # 创建上次消息内容的集合，用于快速查找
        last_contents = {msg["content"] for msg in _self.last_messages}
        
        # 创建历史记录内容的集合，包含用户和助手的消息
        history_contents = {msg["content"] for msg in _self.conversation_history}
        
        # 创建历史用户消息的字典，用于检查回复状态
        history_user_msgs = {}
        for msg in _self.conversation_history:
            if msg["role"] == "user":
                history_user_msgs[msg["content"]] = msg.get("is_replied", False)
        
        # 创建历史助手消息内容的集合，用于参考
        history_assistant_contents = {
            msg["content"] for msg in _self.conversation_history 
            if msg["role"] == "assistant"
        }
        
        for msg in current_messages:
            # 检查是否是新消息（不在上次消息中）且不在历史记录中
            if msg["content"] not in last_contents:
                # 新的内容，但只添加未回复的用户消息
                if msg["role"] == "user":
                    # 创建消息哈希，用于全局跟踪
                    msg_hash = hash(f"{_self.current_contact}:{msg['content']}")
                    
                    # 检查是否已经在全局跟踪集合中
                    if msg_hash in _self.processed_message_hashes:
                        _self.debug_log(f"  跳过已处理过的消息(哈希匹配): {msg['content']}")
                        continue
                        
                    # 检查是否在历史记录中，以及历史记录中的回复状态
                    if msg["content"] in history_user_msgs:
                        # 如果历史记录中已标记为已回复，则当前消息也应标记为已回复
                        if history_user_msgs[msg["content"]]:
                            msg["is_replied"] = True
                            _self.debug_log(f"  将消息标记为已回复(与历史记录一致): {msg['content']}")
                        else:
                            # 历史记录中标记为未回复，保持当前状态
                            if not msg.get("is_replied", False):
                                new_messages.append(msg)
                                # 添加到全局跟踪集合
                                _self.processed_message_hashes.add(msg_hash)
                                _self.debug_log(f"  添加历史记录中未回复的用户消息: {msg['content']}")
                    elif not msg.get("is_replied", False) and msg["content"] not in history_contents:
                        # 完全新的未回复消息
                        new_messages.append(msg)
                        # 添加到全局跟踪集合
                        _self.processed_message_hashes.add(msg_hash)
                        _self.log(f"联系人'{_self.current_contact}'检测到新的未回复用户消息: {msg['content']}")
                elif msg["role"] == "assistant":
                    # 对于助手消息，只记录，不添加到new_messages
                    if msg["content"] in history_assistant_contents:
                        _self.debug_log(f"  跳过已存在的助手回复: {msg['content']}")
                    else:
                        _self.debug_log(f"  跳过新助手消息: {msg['content']}，助手消息只在生成回复时添加")
    
    # 更新联系人消息计数
    _self.contact_message_counts[_self.current_contact] = len(current_messages)
    
    # 记录已处理消息数量
    _self.debug_log(f"全局跟踪集合中已有 {len(_self.processed_message_hashes)} 条消息哈希")
    
    return new_messages

def main2_get_settings(_self):
    """获取当前加载的设置"""
    return _self.settings

def main2_save_chat_summary_settings(_self, chat_summary_settings):
    """保存聊天总结设置"""
    # 更新设置对象
    _self.settings.update(chat_summary_settings)
    # 保存到文件
    _self.storage_manager.save_settings(_self.settings)
    _self.log("聊天总结设置已保存")
    
def main2_save_settings(_self, settings):
    """保存通用设置"""
    # 更新设置对象
    _self.settings.update(settings)
    # 保存到文件
    _self.storage_manager.save_settings(_self.settings)
    _self.log("设置已保存")

    # 如果保存的设置包含固定回复规则，重新加载规则
    if 'fixed_reply_rules' in settings:
        from utils.fixed_reply_processor import reload_fixed_reply_rules
        summary = reload_fixed_reply_rules()
        _self.log(f"固定回复规则已重新加载: {summary['total_rules']}条规则")
    
def main2_generate_chat_summary(_self, contact_name):
    """生成聊天总结"""
    from tkinter import ttk  # 确保在方法内部导入ttk
    
    _self.log(f"正在为联系人 {contact_name} 生成聊天总结...")
    
    # 获取聊天记录
    conversation = _self.conversations.get(contact_name, [])
    if not conversation:
        _self.log(f"找不到联系人 {contact_name} 的聊天记录")
        return
    
    # 获取聊天总结提示词
    chat_summary_prompt = _self.settings.get('chat_summary_prompt06151', '')
    if not chat_summary_prompt:
        _self.log("聊天总结提示词为空，使用默认提示词")
        chat_summary_prompt = "你是一个聊天总结专家，有着丰富的识别问题和提炼要点的能力。\n一下是上次总结的结果{last_summary}\n请将上一次的总结内容和以下与特定联系人的聊天对话新增内容进行合并总结：\n{chat_content}\n\n总结输出格式：\n1.联系人的主要话题和关注点\n2.联系人的语气和情绪\n3.对话中的重要信息和决定\n4.整体总结。"
    

    # 创建进度弹窗
    progress_window = tk.Toplevel(_self.root)
    progress_window.title("聊天总结进度")
    progress_window.geometry("400x200")
    progress_window.resizable(False, False)
    
    # 窗口居中
    window_width = 400
    window_height = 200
    screen_width = _self.root.winfo_screenwidth()
    screen_height = _self.root.winfo_screenheight()
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    # 添加标题
    title_label = ttk.Label(progress_window, text=f"正在为 {contact_name} 生成聊天总结...", font=('微软雅黑', 12, 'bold'))
    title_label.pack(pady=15)
    
    # 添加进度条
    progress = ttk.Progressbar(progress_window, orient="horizontal", length=350, mode="indeterminate")
    progress.pack(pady=15)
    progress.start()
    
    # 添加状态标签
    status_label = ttk.Label(progress_window, text="正在处理聊天记录...", font=('微软雅黑', 10))
    status_label.pack(pady=10)
    
    # 创建AI聊天对象
    api_settings = {
        'api_key': _self.settings.get('api_key', ''),
        'api_base_url': _self.settings.get('api_base_url', 'https://api.openai.com'),
        'model_name': _self.settings.get('model_name', 'gpt-3.5-turbo'),
        'provider': _self.settings.get('provider', 'openai'),
        'bot_id': _self.settings.get('bot_id', ''),
        'token': _self.settings.get('token', ''),
        'system_prompt': "你是一个聊天总结专家",
        'delay': 0.1,
        'debug_mode': _self.debug_mode
    }
    
    # 创建AI聊天对象并设置给处理器
    ai_chat = AiChat(api_settings, stop_flag=lambda: _self.stop_requested06141)
    _self.chat_summary_processor06151.set_ai_chat_module(ai_chat)
    
    # 生成总结
    def on_summary_complete(summary_item):
        if summary_item:
            if "error" in summary_item:
                # 显示错误信息
                error_message = summary_item["error"]
                _self.log(f"联系人 {contact_name} 的聊天总结生成失败: {error_message}")
                # 更新状态和进度条
                status_label.config(text=f"总结生成失败: {error_message}")
                progress.stop()
                
                # 添加关闭按钮
                close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                close_btn.pack(pady=10)
            else:
                _self.log(f"联系人 {contact_name} 的聊天总结生成成功")
                # 更新状态和进度条
                status_label.config(text="总结已完成!")
                progress.stop()
                progress.config(mode="determinate", value=100)
                
                # 添加关闭按钮
                close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                close_btn.pack(pady=10)
                
                # 重新加载聊天总结数据
                if hasattr(_self, 'settings_ui') and _self.settings_ui and hasattr(_self.settings_ui, 'chat_summary_ui06151') and _self.settings_ui.chat_summary_ui06151:
                    _self.settings_ui.chat_summary_ui06151.load_summary_data()
                    _self.log("已重新加载聊天总结数据")
        else:
            _self.log(f"联系人 {contact_name} 的聊天总结生成失败")
            # 更新状态和进度条
            status_label.config(text="总结生成失败!")
            progress.stop()
            
            # 添加关闭按钮
            close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
            close_btn.pack(pady=10)
    
    # 使用线程避免阻塞主线程
    import threading
    summary_thread = threading.Thread(
        target=lambda: _self.chat_summary_processor06151.generate_summary(
            contact_name, 
            conversation, 
            chat_summary_prompt, 
            on_summary_complete
        )
    )
    summary_thread.daemon = True
    summary_thread.start()

def main2_reply_to_message(_self, message_text):
    """对消息进行回复"""
    try:
        # 检查当前联系人是否在不理睬名单中
        if _self.current_contact in _self.settings.get('ignored_contacts', []):
            _self.debug_log(f"联系人 '{_self.current_contact}' 在不理睬名单中，跳过回复")
            return False
            
        api_settings = {
            'api_key': _self.settings.get('api_key', ''),
            'api_base_url': _self.settings.get('api_base_url', 'https://api.openai.com'),
            'model_name': _self.settings.get('model_name', 'gpt-3.5-turbo'),
            'provider': _self.settings.get('provider', 'openai'),
            'bot_id': _self.settings.get('bot_id', ''),
            'token': _self.settings.get('token', ''),
            'delay': _self.settings.get('delay', 0.1),
            'debug_mode': _self.debug_mode
        }
        
        # 创建AI聊天对象
        ai_chat = AiChat(api_settings, stop_flag=lambda: _self.stop_requested06141)
        
        # 设置给聊天总结处理器
        _self.chat_summary_processor06151.set_ai_chat_module(ai_chat)
        
        # 获取过滤后的会话历史（只包含最后总结之后的消息）
        if _self.current_contact:
            filtered_history = _self.get_filtered_conversation_history(_self.current_contact)
        else:
            filtered_history = _self.conversation_history
            _self.debug_log("当前没有联系人，使用完整会话历史")
        
        # 生成回复
        _self.log(f"正在生成回复...")
        reply = ai_chat.generate_reply(filtered_history)
        _self.log(f"回复生成成功，正在输入...")
        
        # 输入回复
        ai_chat.type_reply(reply, _self.input_area)
        
        # 保存回复到对话历史
        assistant_message = {"role": "assistant", "content": reply, "timestamp": int(time.time())}
        _self.conversation_history.append(assistant_message)
        
        # 如果指定了联系人名称，保存到对应的会话中
        if _self.current_contact:
            _self.conversations[_self.current_contact] = _self.conversation_history
            _self.storage_manager.save_conversation(_self.current_contact, _self.conversation_history)
            _self.log(f"用户回复已保存到 {_self.current_contact} 的会话历史中")
            _self.debug_log(f"当前 {_self.current_contact} 的会话历史记录数: {len(_self.conversation_history)}")
        
        # 标记最近的用户消息为已回复
        for i in range(len(_self.conversation_history) - 2, -1, -1):
            msg = _self.conversation_history[i]
            if msg["role"] == "user" and not msg.get("is_replied", False):
                msg["is_replied"] = True
                break
        
        # 清空当前截屏的消息列表，再次获取消息
        time.sleep(1)  # 等待一秒再次获取消息，避免重复检测
        _self.last_messages = []
        
        return True
    except Exception as e:
        _self.log(f"回复消息时出错: {str(e)}")
        return False
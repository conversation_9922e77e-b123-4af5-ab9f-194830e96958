{"api_key": "sk-6b38565acb074790815db2c4661ec121", "api_base_url": "https://api.deepseek.com/v1", "model_name": "deepseek-chat", "system_prompt": "你是一个有用的助手。请用简短、友好的方式回答问题。", "mask_green_bubbles": true, "green_r": 80, "green_g": 200, "green_b": 80, "green_tol": 40, "green_min_area": 100, "text_color_enabled": true, "text_color_r": 25, "text_color_g": 25, "text_color_b": 25, "text_color_tol": 40, "default_system_prompt": "你是一个情感大师，专门分析情感。\n\n你要这样称呼联系人，{联系人名称}\n\n以下是之前与该用户的对话总结，{之前对话总结}", "prompt_templates": [{"keyword": "XXJJ", "prompt": "你是用户的姐姐，你这样称呼用户:{联系人名称}\n以下是你们之前的聊天记录：\n{之前对话总结}"}], "provider": "openai", "delay": 0.1, "bot_id": "7504237352027144231", "token": "8lEzdJ7B-W5NzbJAvISapOBuLOAdB6gJD26p0vr7f9I", "daily_summary_time": "18:00", "daily_summary_prompt": "你是一个聊天总结专家，有这丰富的识别问题和提炼要点的能力。\n\n请对以下聊天对话内容进行总结：\n{chat_content}\n\n总结输出格式：\n1.用户主要的疑问和顾虑\n2.用户的性格和情绪\n3.用户的意向\n4.整体总结\n？？？北包包", "mail_host": "smtp.126.com", "mail_user": "<EMAIL>", "mail_pass": "ZEZPS3AaCRWmba23", "receivers": "<EMAIL>", "accumulated_message_count06151": "1", "chat_summary_prompt06151": "你是一个聊天总结专家，有着丰富的识别问题和提炼要点的能力。以下是上次总结的结果\n{last_summary}\n\n请将上一次的总结内容和以下与特定联系人的聊天对话新增内容进行合并总结：\n{chat_content}\n\n总结输出格式：\n1.联系人的主要话题和关注点\n2.联系人的语气和情绪\n3.对话中的重要信息和决定\n4.整体总结。\"\n", "enable_emojis_lottery": true, "emojis_lottery_rate": "0.9", "emojis_folder_path": "/Users/<USER>/Desktop/表情文件夹", "files_folder_path": "/Users/<USER>/Desktop/文件文件夹", "emojis_or_file_prompt": "请帮我根据现在的聊天场景，如果需要则在聊天中发送一个表情或文件。请从以下文件中选择一个，然后使用send_emojis或send_file工具发送它：\n表情有： '搞怪.png', '滑稽.png', '亲亲.png', '阴险.png'。\n文件有： '二维码.jpg', '上海/软件使用免责声明.pdf', '上海/视频专网信息安全脆弱性检测方法.doc'\n\n\n请根据聊天上下文选择一个适合的文件发送。发送时请使用文件的完整路径。如果不需要发送，则不需要调用工具", "interval": 1.0, "preview_interval": 1.0, "center_margin": 50, "line_spacing": 30, "red_r": 231, "red_g": 94, "red_b": 88, "red_tol": 5, "debug_mode": false, "mouse_interval": 1.0, "voice_wait": 6.0, "voice_logo_mask_range": 20, "voice_bubble_offset": 100, "ignored_contacts": ["测试123"], "chat_area": [409, 202, 1079, 799], "input_area": [674, 869, 1003, 946], "contact_area": [213, 199, 224, 991], "contact_name_area": [421, 151, 939, 185], "unread_bubble_area": [112, 267, 162, 299], "search_area": [182, 158, 360, 184], "noise_areas": [], "active_dialogue_prompt": "你是一个能够生成个性化主动对话的助手。请根据以下信息为我生成一条自然、友好且有针对性的主动对话消息：\n\n联系人: {contact_name}\n最新聊天总结: {summary}\n最近聊天记录: {chat_history}\n\n请生成一条合适的主动对话消息，长度控制在20字以内，语气自然友好，内容要与对方最近的聊天相关。", "contact_filter_mode": "reply_only", "ai_brain_url07091": "http://127.0.0.1:8001", "ai_brain_key07091": "cb742e62b1b34c668c2db8e0ac53c9701194fc1f664140238bb58a6e6e98dada", "unique_id_area": [420, 150, 939, 184], "fixed_reply_rules": [{"id": "3846ff48-0276-4e8d-a6d4-42f8e241cdfd", "name": "测试1下", "keyword": "我不开心", "match_dimensions": ["user_message"], "actions": [{"type": "text", "content": "不要不开心了，都会过去的", "delay": 1.0}, {"type": "file", "file_path": "/Users/<USER>/Desktop/表情文件夹/亲亲.png", "delay": 1.0}], "enabled": false}, {"id": "01c16da9-2af1-45be-b523-a25f2f7a1d54", "name": "第二个测试", "keyword": "等于", "match_dimensions": ["ai_reply"], "actions": [{"type": "text", "content": "测试通过", "delay": 1.0}, {"type": "file", "file_path": "/Users/<USER>/Desktop/表情文件夹/阴险.png", "delay": 1.0}], "enabled": true}]}
# 屏幕聊天监控助手

这是一个跨平台的Python应用程序，可以监控屏幕上指定区域的聊天内容，识别文字并自动回复。

## 功能特点

- 截取桌面指定区域的图像
- 使用PaddleOCR进行OCR识别聊天记录
- 检测新的聊天记录
- 调用AI生成回复(预留接口)
- 使用PyAutoGUI模拟键盘输入回复，并添加延迟
- 跨平台支持(Windows, macOS, Linux)

## 安装要求

1. 确保已安装Python 3.7+
2. 安装依赖包:
```
pip install -r requirements.txt
```

### PaddlePaddle安装说明

不同平台上安装PaddlePaddle可能需要特殊处理：

- **MacOS (特别是M1/M2芯片)**:
  ```
  python -m pip install paddlepaddle==2.4.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
  ```

- **其他平台**:
  ```
  pip install paddlepaddle>=2.4.2
  ```

- 如果安装后仍有问题，请参考[PaddlePaddle官方安装指南](https://www.paddlepaddle.org.cn/install/quick)

## 使用方法

1. 运行程序:
```
python main.py
```

2. 点击"选择聊天区域"按钮，然后在屏幕上框选聊天区域。
3. 点击"开始监控"按钮开始监控聊天区域。
4. 程序会自动识别新消息，并生成回复。
5. 回复会通过模拟键盘输入的方式发送到当前聊天窗口。
6. 点击"停止监控"按钮可以停止监控。

## 配置选项

- 输入延迟：每个字符输入之间的延迟时间（秒）
- 监控间隔：屏幕截图与OCR识别的间隔时间（秒）

## 自定义AI接口

要集成自己的AI回复生成接口，请修改`generate_reply`方法。

## 常见问题解决

如果遇到`ModuleNotFoundError: No module named 'paddle.utils'`错误，请尝试：

1. 卸载并重新安装paddlepaddle:
   ```
   pip uninstall paddlepaddle
   pip install paddlepaddle==2.4.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

2. 确保安装了完整的paddlepaddle，而不是paddlepaddle-tiny等精简版本 
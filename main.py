import json
import sys
import tkinter as tk
import queue
import time
import os
import threading
import cv2
import numpy as np
from PIL import Image, ImageTk
import markdown
import platform
import re
from tkinter import ttk, messagebox
from pynput.mouse import <PERSON><PERSON>, Controller
from utils.voice_read_detector import find_red_dot_with_nms
import pyautogui

# 导入自定义模块
from gui.ui_main import MainUI
from gui.settings_ui import SettingsUI
from gui.selection_ui import SelectionUI
from gui.history_ui import HistoryUI
from monitor.area_monitor import AreaMonitor
from ocr.message_extractor import MessageExtractor
from utils.storage import StorageManager
from utils.ai_chat import AiChat
from utils.image_processor import capture_screen_area
from utils.system_tools import check_lisence
from utils.chat_summary_processor06151 import ChatSummaryProcessor06151
from utils.voice_read_detector import find_red_dot_with_nms
from utils.mouse_takeover_detector06191 import MouseTakeoverDetector06191  # 导入鼠标接管检测器
from utils.active_dialogue_processor06242 import ActiveDialogueProcessor06242  # 导入主动对话处理器
from utils.fixed_reply_processor import get_fixed_reply_processor, reload_fixed_reply_rules  # 导入固定回复处理器

from main2 import *

class ScreenChatBot:
    def __init__(self, root):
        """
        应用程序主控制器
        :param root: Tkinter根窗口
        """
        self.root = root
        
        # 创建UI回调函数字典
        self.callbacks = {
            'select_areas': self.select_areas,
            'start_monitoring': self.start_monitoring,
            'stop_monitoring': self.stop_monitoring,
            'toggle_debug': self.toggle_debug,
            'clear_log': self.clear_log,
            'test_reply': self.test_reply,
            'save_panel_settings': self.save_panel_settings,
            'toggle_green_mask': self.toggle_green_mask,
            'toggle_text_color': self.toggle_text_color,
            'test_green_mask': self.test_green_mask,
            'test_text_color': self.test_text_color,
            'show_conversation_history': self.show_conversation_history,
            'save_api_settings': self.save_api_settings,
            'reset_areas': self.reset_areas,
            'get_settings': self.get_settings,
            'save_prompt_templates': self.save_prompt_templates,
            'save_daily_summary_settings': self.save_daily_summary_settings,
            'run_summary_now': self.run_summary_now,
            'save_chat_summary_settings': self.save_chat_summary_settings,
            'generate_chat_summary': self.generate_chat_summary,
            'save_settings': self.save_settings,
            'test_voice06181': self.test_voice06181  # 添加测试语音功能回调
        }
        
        # 初始化UI
        self.ui = MainUI(root, self.callbacks)
        self.settings_ui = None
        
        # 初始化状态变量
        self.selection_done = False
        self.monitoring = False
        self.is_replying = False
        self.debug_mode = False
        self.reply_start_time = 0
        self.reply_timeout = 300
        self.contact_switch_triggered = False  # 联系人切换触发标志
        self.contact_switching = False         # 正在切换联系人标志
        self.stop_requested06141 = False  # 确保正确初始化为False
        self.is_exec_chat_area = False  # 是否执行聊天区域处理
        
        # 聊天总结模块 (新增)
        self.chat_summary_processor06151 = ChatSummaryProcessor06151()
        
        # 鼠标接管检测器 (新增)
        self.mouse_takeover_detector06191 = MouseTakeoverDetector06191(self)
        
        # 主动对话处理器 (新增)
        self.active_dialogue_processor06242 = ActiveDialogueProcessor06242(self)
        
        # 区域坐标
        self.chat_area = None
        self.input_area = None
        self.contact_area = None
        self.contact_name_area = None  # 添加联系人姓名区域
        self.unread_bubble_area = None  # 新增未读气泡区域
        self.search_area = None  # 添加搜索选区
        self.noise_areas = []
        self.screen_center_x = 0
        
        # 初始化组件
        self.message_extractor = MessageExtractor()
        self.storage_manager = StorageManager()
        self.area_monitor = AreaMonitor(self)

        # 初始化固定回复处理器
        self.fixed_reply_processor = get_fixed_reply_processor()

        # 设置输入区域坐标（如果已经选择了区域）
        if hasattr(self, 'input_area') and self.input_area:
            self.fixed_reply_processor.set_input_area(self.input_area)
        
        # 加载设置
        self.settings = self.storage_manager.load_settings()
        self.debug_mode = False
        
        # 加载区域设置
        areas = self.storage_manager.load_areas()
        self.chat_area = areas.get('chat_area')
        self.input_area = areas.get('input_area')
        self.contact_area = areas.get('contact_area')
        self.contact_name_area = areas.get('contact_name_area')
        self.unread_bubble_area = areas.get('unread_bubble_area')
        self.search_area = areas.get('search_area')
        self.noise_areas = areas.get('noise_areas', [])
        
        # 根据已加载区域设置判断是否完成区域选择
        self.selection_done = (self.chat_area is not None and self.input_area is not None)
        
        # 验证每日总结提示词是否正确加载
        if 'daily_summary_prompt' in self.settings:
            print(f"已从settings.json加载每日总结提示词: {self.settings['daily_summary_prompt'][:50]}...")
        else:
            print("警告: 未在settings.json中找到每日总结提示词")
        
        # 初始化日志队列
        self.log_queue = queue.Queue()
        
        # 多人会话管理
        self.current_contact = None  # 当前联系人昵称
        self.conversations = {}  # 存储多个会话的字典 {昵称: 对话历史列表}
        self.contact_message_counts = {}  # 存储每个联系人的消息数量
        self.conversation_history = []  # 当前对话历史
        self.last_messages = []  # 上一次识别到的消息
        self.last_message_count = 0
        
        # 每日总结定时器
        self.summary_scheduled = False
        
        # 加载所有对话历史
        self.load_conversations()
        
    def setup_ui(self):
        """设置UI组件"""
        # 先创建设置界面
        self.settings_ui = SettingsUI(self.root, self.callbacks)
        
        # 为设置选项卡提供回调
        def settings_tab_callback(parent):
            # 添加调试日志
            print("=== settings_tab_callback 被调用 ===")
            print(f"parent类型: {type(parent)}")
            
            # 创建设置选项卡内容
            settings_frame = self.settings_ui.create_settings_tab(parent)
            
            # 确保区域设置显示正确
            areas = {
                'chat_area': self.chat_area,
                'input_area': self.input_area,
                'contact_area': self.contact_area,
                'contact_name_area': self.contact_name_area,
                'unread_bubble_area': self.unread_bubble_area,
                'search_area': self.search_area,  # 添加搜索选区
                'noise_areas': self.noise_areas
            }
            self.settings_ui.update_area_tree(areas)
            
            # 在UI组件创建完成后，再设置API参数
            self.settings_ui.set_api_settings(self.settings)
            
            print("=== settings_tab_callback 执行完毕 ===")
            return settings_frame
            
        self.callbacks['create_settings_tab'] = settings_tab_callback
        
        # 添加获取设置的回调函数
        self.callbacks['get_settings'] = self.get_settings
        
        # 添加保存提示词模板的回调函数
        self.callbacks['save_prompt_templates'] = self.save_prompt_templates
        
        # 添加保存控制面板设置的回调函数
        self.callbacks['save_panel_settings'] = self.save_panel_settings
        
        # 重要：在所有回调设置完成后，再创建主界面组件
        self.ui.create_widgets()
        
        # 如果已经加载了区域设置，启用开始监控按钮
        if self.selection_done:
            self.ui.enable_start_button(True)
            self.ui.enable_test_reply_button(True)
            self.log("已从设置文件加载区域配置，监控功能已启用")
        
        # 添加选项卡切换事件处理
        def on_tab_change(event):
            self.debug_log(f"选项卡切换: {event.widget.index('current')}")
            # 如果切换到设置选项卡，确保设置界面已正确加载
            if event.widget.index("current") == 1:  # 设置选项卡索引为1
                self.debug_log("切换到设置选项卡")
                # 更新基本设置中的区域树
                self.update_settings_area_tree()
                
                # 更新设置UI中的内容
                if self.settings_ui:
                    # 更新区域树
                    areas = {
                        'chat_area': self.chat_area,
                        'input_area': self.input_area,
                        'contact_area': self.contact_area,
                        'contact_name_area': self.contact_name_area,
                        'unique_id_area': self.settings.get('unique_id_area'),  # 添加唯一标识区
                        'unread_bubble_area': self.unread_bubble_area,
                        'search_area': self.search_area,
                        'noise_areas': self.noise_areas
                    }
                    self.settings_ui.update_area_tree(areas)
        
        # 绑定选项卡切换事件
        self.ui.tab_control.bind("<<NotebookTabChanged>>", on_tab_change)
        
        # 启动日志处理
        self.root.after(100, self.process_log_queue)
        
        # 初始化日志信息
        self.log("程序已启动，请使用<选择区域>按钮开始...")
        
        # 启动每日总结定时任务
        self.schedule_daily_summary()
        
        # 从设置中加载控制面板参数
        if hasattr(self.ui, 'set_settings'):
            self.ui.set_settings(self.settings)
            self.log("已从settings.json加载控制面板设置")

    def save_panel_settings(self):
        """保存控制面板设置"""
        # 获取当前控制面板设置
        panel_settings = self.ui.get_settings()
        
        # 更新设置字典
        self.settings.update(panel_settings)
        
        # 保存到文件
        if self.storage_manager.save_settings(self.settings):
            self.log("控制面板设置已保存到settings.json")
        else:
            self.log("保存控制面板设置失败")
            
        # 调试信息
        self.debug_log(f"已保存的控制面板设置: {list(panel_settings.keys())[:5]}...")
        
        return True
    
    def process_log_queue(self):
        """处理日志队列，减少UI阻塞"""
        try:
            while not self.log_queue.empty():
                log_entry = self.log_queue.get_nowait()
                if hasattr(self.ui, 'add_log'):
                    self.ui.add_log(log_entry)
        except Exception:
            pass
        finally:
            # 安排下一次处理
            self.root.after(100, self.process_log_queue)
    
    def log(self, message):
        """添加日志到日志框"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 将日志放入队列，而不是直接更新UI
        self.log_queue.put(log_entry)
        
        # 也打印到控制台
        print(log_entry)
    
    def debug_log(self, message):
        """只在调试模式下记录的日志"""
        if self.debug_mode:
            self.log(f"[DEBUG] {message}")
    
    def toggle_debug(self):
        """切换调试模式状态"""
        self.debug_mode = self.ui.debug_var.get()
        self.log(f"调试模式已{'开启' if self.debug_mode else '关闭'}")
    
    def clear_log(self):
        """清除日志内容"""
        self.ui.clear_log()
    
    def select_areas(self):
        """开始区域选择流程"""
        # 前置检查
        if self.monitoring:
            self.log("请先停止监控再重新选择区域")
            return
        
        # 创建选择UI实例
        selection_ui = SelectionUI(self.selection_callback)
        
        # 开始选择流程
        selection_ui.start_selection(self.root)
    
    def selection_callback(self, result):
        """
        选择区域后的回调函数
        :param result: 选择结果
        """
        print("\n选择回调被调用...")
        print(f"接收到的结果类型: {type(result)}")
        print(f"接收到的结果: {result}")
        
        # 更新各个区域坐标
        try:
            self.chat_area = result.get('chat_area')
            self.input_area = result.get('input_area')
            self.contact_area = result.get('contact_area')
            self.contact_name_area = result.get('contact_name_area')
            self.unread_bubble_area = result.get('unread_bubble_area')
            self.search_area = result.get('search_area')  # 添加搜索选区
            self.noise_areas = result.get('noise_areas', [])
            
            # 计算屏幕中心X坐标（用于定位联系人列表中的联系人）
            screen_size = pyautogui.size()
            self.screen_center_x = screen_size[0] // 2
            
            # 处理消息区域可能需要的额外处理...
            
            # 恢复主窗口显示
            self.root.deiconify()
            
            # 记录选择成功
            self.selection_done = True
            self.log("区域选择完成")

            # 更新固定回复处理器的输入区域坐标
            if hasattr(self, 'fixed_reply_processor') and self.input_area:
                self.fixed_reply_processor.set_input_area(self.input_area)
            
            # 保存区域设置
            self.save_settings({
                'chat_area': self.chat_area,
                'input_area': self.input_area,
                'contact_area': self.contact_area,
                'contact_name_area': self.contact_name_area,
                'unique_id_area': result.get('unique_id_area'),  # 添加唯一标识区
                'unread_bubble_area': self.unread_bubble_area,
                'search_area': self.search_area,
                'noise_areas': self.noise_areas
            })
            
            # 如果有设置UI，更新其中的区域树
            if self.settings_ui:
                print("正在更新设置UI中的区域树...")
                areas = {
                    'chat_area': self.chat_area,
                    'input_area': self.input_area,
                    'contact_area': self.contact_area,
                    'contact_name_area': self.contact_name_area,
                    'unique_id_area': result.get('unique_id_area'),  # 添加唯一标识区
                    'unread_bubble_area': self.unread_bubble_area,
                    'search_area': self.search_area,
                    'noise_areas': self.noise_areas
                }
                self.settings_ui.update_area_tree(areas)
                print("区域树更新完成")
                
            # 启用开始监控按钮
            self.ui.enable_start_button(True)
            self.ui.enable_test_reply_button(True)
                
            # 保存unique_id_area到settings中
            if result.get('unique_id_area'):
                self.settings['unique_id_area'] = result.get('unique_id_area')
                
        except Exception as e:
            print(f"处理选择结果时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
            self.selection_done = False
            self.log(f"区域选择出错: {str(e)}")
    
    def update_settings_area_tree(self):
        """更新设置界面中的区域树"""
        if self.settings_ui and hasattr(self.settings_ui, 'update_area_tree'):
            areas = {
                'chat_area': self.chat_area,
                'input_area': self.input_area,
                'contact_area': self.contact_area,
                'contact_name_area': self.contact_name_area,
                'unique_id_area': self.settings.get('unique_id_area'),  # 添加唯一标识区
                'unread_bubble_area': self.unread_bubble_area,
                'search_area': self.search_area,
                'noise_areas': self.noise_areas
            }
            self.settings_ui.update_area_tree(areas)
    
    def reset_areas(self):
        """重置所有区域选择"""
        if self.monitoring:
            self.log("请先停止监控再重置区域")
            return
            
        # 确认重置
        result = messagebox.askokcancel("确认重置", "确定要重置所有区域设置吗？")
        if not result:
            return
            
        # 重置区域坐标
        self.chat_area = None
        self.input_area = None
        self.contact_area = None
        self.contact_name_area = None
        self.unread_bubble_area = None
        self.search_area = None  # 添加搜索选区
        self.noise_areas = []
        
        # 重置选择状态
        self.selection_done = False
        
        # 保存空设置
        self.save_settings({
            'chat_area': None,
            'input_area': None,
            'contact_area': None,
            'contact_name_area': None,
            'unique_id_area': None,  # 添加唯一标识区
            'unread_bubble_area': None,
            'search_area': None,
            'noise_areas': []
        })
        
        # 更新设置UI的区域树
        if self.settings_ui:
            areas = {
                'chat_area': None,
                'input_area': None,
                'contact_area': None,
                'contact_name_area': None,
                'unique_id_area': None,  # 添加唯一标识区
                'unread_bubble_area': None,
                'search_area': None,
                'noise_areas': []
            }
            self.settings_ui.update_area_tree(areas)
        
        self.log("已重置所有区域设置")
    
    def start_monitoring(self):
        """开始监控所选区域"""
        # 检查是否已经选择了区域
        if not self.selection_done:
            self.log("请先选择监控区域")
            return

        # 检查是否已经在监控中
        if self.monitoring:
            self.log("监控已在运行中，请先停止当前监控")
            return
        
        # 重置停止请求标志
        self.stop_requested06141 = False
        
        self.log("正在启动监控...")
        # 记录当前活跃线程数
        active_threads_before = threading.active_count()
        self.debug_log(f"启动监控前活跃线程数: {active_threads_before}")
        
        # 尝试启动区域监控
        if not self.area_monitor.start_monitoring():
            self.log("监控启动失败")
            return
            
        # 启动鼠标接管检测器
        self.mouse_takeover_detector06191.start_monitoring06191()
        
        # 记录启动后的线程数
        active_threads_after = threading.active_count()
        self.debug_log(f"启动监控后活跃线程数: {active_threads_after}, 增加了 {active_threads_after - active_threads_before} 个线程")
            
        self.monitoring = True
        self.ui.update_monitoring_status(True)
        self.log("已开始监控")
        
        # 在启动监控后立即主动识别当前联系人
        if self.current_contact is None:
            self.identify_current_contact()
    
    def identify_current_contact(self):
        """主动识别当前联系人名称"""
        try:
            # 等待一小段时间让UI稳定
            time.sleep(0.5)
            
            self.log("正在主动识别当前联系人...")
            
            # 检查是否有联系人姓名区域
            if not self.contact_name_area:
                self.log("未设置联系人姓名区域，无法识别联系人")
                return
                
            # 捕获联系人姓名区域
            nx1, ny1, nx2, ny2 = self.contact_name_area
            
            # 使用AreaMonitor的capture_area方法
            name_img = self.area_monitor.capture_area(nx1, ny1, nx2, ny2)
            
            # 在debug模式下保存联系人姓名区域截图
            if self.debug_mode:
                debug_dir = "debug_screenshots"
                os.makedirs(debug_dir, exist_ok=True)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                name_img_path = os.path.join(debug_dir, f"initial_contact_name_area_{timestamp}.png")
                
                # Ensure name_img is a valid NumPy array
                if name_img is None:
                    self.debug_log("Error: name_img is None")
                else:
                    if not isinstance(name_img, np.ndarray):
                        self.debug_log(f"Converting name_img from {type(name_img)} to numpy array")
                        try:
                            name_img = np.array(name_img)
                        except Exception as e:
                            self.debug_log(f"Cannot convert to numpy array: {str(e)}")
                            
                    # Ensure proper image format (uint8)
                    if name_img.dtype != np.uint8:
                        self.debug_log(f"Converting image from {name_img.dtype} to uint8")
                        name_img = name_img.astype(np.uint8)
                        
                    cv2.imwrite(name_img_path, name_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    self.debug_log(f"已保存初始联系人姓名区域截图: {name_img_path}")
            
            # 使用OCR识别联系人姓名
            result = self.message_extractor.ocr.ocr(name_img, cls=True)
            
            # 提取识别出的文本
            contact_name = ""
            if result and len(result) > 0:
                # 使用第一个识别结果作为联系人姓名
                for line in result:
                    if isinstance(line, list) and len(line) > 0:
                        for item in line:
                            if len(item) >= 2 and item[1] is not None and len(item[1]) >= 2:
                                text = item[1][0].strip()
                                if text:
                                    contact_name += text
            
            # Windows平台特殊处理，如果OCR失败，尝试使用extract_contact_name方法
            if not contact_name and platform.system() == 'Windows':
                self.debug_log("直接OCR失败，尝试使用extract_contact_name方法")
                try:
                    # 使用专门的联系人提取方法
                    ocr_params = {'debug_mode': self.debug_mode}
                    contact_name = self.message_extractor.extract_contact_name(name_img, ocr_params)
                except Exception as e:
                    self.debug_log(f"extract_contact_name方法也失败: {str(e)}")
            
            if contact_name:
                self.log(f"识别到当前联系人: {contact_name}")
                # 切换到识别的联系人
                switch_result, _ = self.switch_contact(contact_name)
                if switch_result:
                    self.log(f"已成功切换到联系人: {contact_name}")
                else:
                    self.log("切换联系人失败")
            else:
                self.log("未能识别当前联系人，将使用'未知联系人'")
                switch_result, _ = self.switch_contact("未知联系人")
                if switch_result:
                    self.debug_log("已切换到'未知联系人'")
        
        except Exception as e:
            self.log(f"识别当前联系人时出错: {str(e)}")
            import traceback
            self.debug_log(traceback.format_exc())
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            self.debug_log("监控已经是停止状态，无需再次停止")
            return
        
        self.log("正在停止监控...")
        # 记录当前活跃线程数
        active_threads_before = threading.active_count()
        self.debug_log(f"停止监控前活跃线程数: {active_threads_before}")
            
        # 先设置停止标志，以便各个线程能够尽快退出
        self.stop_requested06141 = True
        
        # 停止区域监控
        if not self.area_monitor.stop_monitoring():
            self.log("监控停止失败")
            return
            
        # 停止鼠标接管检测器
        self.mouse_takeover_detector06191.stop_monitoring06191()
        
        # 如果正在生成回复或键入字符，则停止
        if self.is_replying:
            self.is_replying = False
            self.log("停止监控：已中断正在进行的回复生成")
        
        # 更新UI状态和应用状态
        self.monitoring = False
        self.ui.update_monitoring_status(False)
        
        # 记录停止后的线程数
        time.sleep(0.5)  # 给线程一点时间来退出
        active_threads_after = threading.active_count()
        self.debug_log(f"停止监控后活跃线程数: {active_threads_after}, 减少了 {active_threads_before - active_threads_after} 个线程")
        
        self.log("已停止监控")
    
    def test_reply(self):
        """测试回复功能"""
        if self.selection_done:
            if not self.current_contact:
                tk.messagebox.showinfo("提示", "请先进行聊天监控，等待系统识别联系人后再测试回复")
                return
                
            # 如果正在回复，不允许再次发起回复
            if self.is_replying:
                self.log("正在回复中，请等待当前回复完成")
                return
                
            test_message = f"这是发给{self.current_contact}的测试消息"
            self.log(f"测试回复功能，联系人: {self.current_contact}, 模拟消息: {test_message}")
            
            # 设置回复标志
            self.is_replying = True
            self.reply_start_time = time.time()
            
            # 重置停止请求标志
            self.stop_requested06141 = False
            
            # 将测试消息添加到对话历史（作为用户消息），并标记为未回复
            test_msg = {
                "role": "user", 
                "content": test_message, 
                "is_replied": False,
                "timestamp": int(time.time())  # 添加时间戳
            }
            self.conversation_history.append(test_msg)
            self.log(f"已添加测试用户消息到对话历史")
            
            # 更新当前联系人的会话历史
            self.conversations[self.current_contact] = self.conversation_history
            
            # 获取针对当前联系人的系统提示词
            system_prompt = self.get_system_prompt_for_contact(self.current_contact)
            # 确保system_prompt不为None，如果是None则使用空字符串
            if system_prompt is None:
                system_prompt = ""
                self.debug_log("警告：获取到的system_prompt为None，已设置为空字符串")
            
            self.debug_log(f"联系人'{self.current_contact}'使用系统提示词: {system_prompt[:50]}..." if system_prompt and len(system_prompt) > 50 else system_prompt)
            
            # 创建AI聊天实例
            api_settings = {
                'provider': self.settings.get('provider', 'openai'),
                'api_key': self.settings.get('api_key', ''),
                'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
                'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
                'bot_id': self.settings.get('bot_id', ''),
                'token': self.settings.get('token', ''),
                'system_prompt': system_prompt,
                'delay': self.ui.delay_var.get(),
                'debug_mode': self.debug_mode
            }
            self.debug_log(f"测试回复使用API提供商: {api_settings['provider']}")
            ai_chat = AiChat(api_settings, stop_flag=lambda: self.stop_requested06141)
            
            # 获取过滤后的会话历史（只包含最后总结之后的消息）
            filtered_history = self.get_filtered_conversation_history(self.current_contact)
            
            # 生成回复
            reply_list = ai_chat.generate_reply(filtered_history, self.input_area)
            reply = reply_list[-1]["content"]
            # 模拟输入回复
            ai_chat.type_reply(reply, self.input_area, self)
            

            self.conversation_history += reply_list
            self.log(f"【助手回复】已添加到对话历史: {reply[:50]}..." if len(reply) > 50 else reply)
            
            # 标记测试消息为已回复
            test_msg["is_replied"] = True
            self.log("测试消息已标记为已回复")
            
            # 更新并保存会话历史
            self.conversations[self.current_contact] = self.conversation_history
            self.storage_manager.save_conversation(self.current_contact, self.conversation_history)
            
            # 清除回复标志
            self.is_replying = False
            self.log("测试回复完成")
        else:
            self.log("请先选择区域后再测试回复功能")
    
    def update_preview(self):
        """更新预览区域"""
        if self.selection_done and self.chat_area:
            try:
                # 截取屏幕区域
                x1, y1, x2, y2 = self.chat_area
                screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
                
                # 更新UI预览
                self.ui.update_preview(screenshot)
                
                return True
            except Exception as e:
                self.log(f"更新预览失败: {str(e)}")
                return False
        return False
    
    def toggle_green_mask(self):
        """切换是否启用绿色气泡遮罩"""
        mask_green = self.ui.mask_green_var.get()
        self.log(f"{'启用' if mask_green else '禁用'}绿色气泡遮罩")
    
    def toggle_text_color(self):
        """切换是否启用文字颜色过滤"""
        text_color_enabled = self.ui.text_color_enabled_var.get()
        self.log(f"{'启用' if text_color_enabled else '禁用'}文字颜色过滤")
        if text_color_enabled:
            r = self.ui.text_color_r_var.get()
            g = self.ui.text_color_g_var.get()
            b = self.ui.text_color_b_var.get()
            tol = self.ui.text_color_tol_var.get()
            self.log(f"文字颜色设置: R({r}), G({g}), B({b}), 容差({tol})")
    
    def test_green_mask(self):
        """测试绿色气泡遮罩功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试绿色气泡遮罩功能")
            return
            
        self.log("测试绿色气泡遮罩功能...")
        try:
            # 更新绿色气泡参数
            green_r = self.ui.green_r_var.get()
            green_g = self.ui.green_g_var.get()
            green_b = self.ui.green_b_var.get()
            green_tol = self.ui.green_tol_var.get()
            green_min_area = self.ui.green_min_area_var.get()
            
            self.log(f"当前绿色气泡参数: R({green_r}), G({green_g}), B({green_b}), 容差({green_tol}), 最小面积({green_min_area})")
            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存原始截图
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"test_original_{timestamp}.png")
            cv2.imwrite(original_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"原始截图已保存: {original_path}")
            
            # 应用绿色气泡遮罩
            from utils.image_processor import preprocess_image_mask_green_bubbles
            green_params = {
                'green_r': green_r,
                'green_g': green_g,
                'green_b': green_b,
                'green_tol': green_tol,
                'green_min_area': green_min_area,
                'debug_mode': True
            }
            # 获取返回的三个值：处理后的图像和可能的遮罩中心点坐标
            processed_img, mask_center_x, mask_center_y = preprocess_image_mask_green_bubbles(screenshot, green_params)
            
            if mask_center_x is not None and mask_center_y is not None:
                self.log(f"检测到遮罩中心点坐标: ({mask_center_x}, {mask_center_y})")
            
            # Check and convert processed_img if needed
            if processed_img is None:
                self.log("Error: processed_img is None")
                return
                
            self.debug_log(f"processed_img type: {type(processed_img)}")
            
            # Ensure processed_img is a NumPy array
            if not isinstance(processed_img, np.ndarray):
                self.log(f"Converting processed_img from {type(processed_img)} to numpy array")
                try:
                    if isinstance(processed_img, tuple) and len(processed_img) == 3:
                        # If it's a RGB/BGR tuple, create an image of the appropriate size filled with this color
                        r, g, b = processed_img
                        # Create a small 1x1 pixel array with the RGB color
                        processed_img = np.zeros((1, 1, 3), dtype=np.uint8)
                        processed_img[0, 0] = [b, g, r]  # OpenCV uses BGR order
                        self.log(f"Created a single-pixel image with color {processed_img} from tuple")
                    else:
                        processed_img = np.array(processed_img)
                except Exception as e:
                    self.log(f"Cannot convert to numpy array: {str(e)}")
                    self.log("Creating a placeholder empty image instead")
                    # Create a small blank image as a placeholder
                    processed_img = np.zeros((100, 100, 3), dtype=np.uint8)
            
            # Ensure proper image format (uint8)
            if processed_img.dtype != np.uint8:
                self.log(f"Converting image from {processed_img.dtype} to uint8")
                processed_img = processed_img.astype(np.uint8)
            
            # Save processed image
            processed_path = os.path.join(debug_dir, f"test_masked_{timestamp}.png")
            cv2.imwrite(processed_path, processed_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"处理后图像已保存: {processed_path}")
            
            # 计算处理前后的差异
            diff = cv2.absdiff(screenshot, processed_img)
            diff_sum = np.sum(diff)
            if diff_sum > 0:
                self.log(f"检测到差异，遮罩功能有效。差异值: {diff_sum}")
                
                # 保存差异图像
                diff_path = os.path.join(debug_dir, f"test_diff_{timestamp}.png")
                cv2.imwrite(diff_path, diff, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                self.log(f"差异图像已保存: {diff_path}")
            else:
                self.log("未检测到差异，遮罩功能可能无效。请调整参数后重试。")
                
            self.log("绿色气泡遮罩测试完成，请查看debug_screenshots目录中的测试图像")
            
        except Exception as e:
            self.log(f"测试绿色气泡遮罩时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
    
    def test_text_color(self):
        """测试文字颜色过滤功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试文字颜色过滤功能")
            return
            
        self.log("测试文字颜色过滤功能...")
        try:
            # 获取文字颜色参数
            text_color_r = self.ui.text_color_r_var.get()
            text_color_g = self.ui.text_color_g_var.get()
            text_color_b = self.ui.text_color_b_var.get()
            text_color_tol = self.ui.text_color_tol_var.get()
            
            self.log(f"当前文字颜色参数: R({text_color_r}), G({text_color_g}), B({text_color_b}), 容差({text_color_tol})")
            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存原始截图
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            original_path = os.path.join(debug_dir, f"text_color_test_original_{timestamp}.png")
            cv2.imwrite(original_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"原始截图已保存: {original_path}")
            
            # 应用文字颜色过滤
            from utils.image_processor import filter_text_by_color
            color_params = {
                'text_color_r': text_color_r,
                'text_color_g': text_color_g,
                'text_color_b': text_color_b,
                'text_color_tol': text_color_tol,
                'debug_mode': True
            }
            filtered_img = filter_text_by_color(screenshot, color_params)
            
            # 保存处理后的图像
            filtered_path = os.path.join(debug_dir, f"text_color_test_filtered_{timestamp}.png")
            cv2.imwrite(filtered_path, filtered_img, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"过滤后图像已保存: {filtered_path}")
            
            # 创建颜色范围掩码用于显示
            # 在OpenCV中是BGR顺序
            lower_color_bgr = np.array([max(0, text_color_b-text_color_tol), max(0, text_color_g-text_color_tol), max(0, text_color_r-text_color_tol)])
            upper_color_bgr = np.array([min(255, text_color_b+text_color_tol), min(255, text_color_g+text_color_tol), min(255, text_color_r+text_color_tol)])
            
            # 创建BGR掩码
            color_mask = cv2.inRange(screenshot, lower_color_bgr, upper_color_bgr)
            
            # 保存掩码图像
            mask_path = os.path.join(debug_dir, f"text_color_test_mask_{timestamp}.png")
            cv2.imwrite(mask_path, color_mask, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"颜色掩码已保存: {mask_path}")
            
            # 显示处理后的图像
            self.ui.update_preview(filtered_img)
            self.log("已在预览区域显示过滤后的图像")
            
            # 更新UI
            self.ui.text_color_enabled_var.set(True)
            
        except Exception as e:
            self.log(f"测试文字颜色过滤出错: {str(e)}")
    
    def test_voice06181(self):
        """测试语音消息红点检测和处理功能"""
        if not self.selection_done:
            self.log("请先选择区域后再测试语音功能")
            return
            
        self.log("测试语音消息红点检测功能...")
        try:

            
            # 截取屏幕
            x1, y1, x2, y2 = self.chat_area
            screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
            
            if screenshot is None or screenshot.size == 0:
                self.log("截图失败，无法测试")
                return
                
            # 保存截图用于检测
            debug_dir = "debug_screenshots"
            os.makedirs(debug_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            debug_img_path = os.path.join(debug_dir, f"voice_test_{timestamp}.png")
            
            # Ensure screenshot is a valid NumPy array
            if screenshot is None:
                self.log("Error: screenshot is None")
                return
                
            if not isinstance(screenshot, np.ndarray):
                self.log(f"Converting screenshot from {type(screenshot)} to numpy array")
                try:
                    screenshot = np.array(screenshot)
                except Exception as e:
                    self.log(f"Cannot convert to numpy array: {str(e)}")
                    return
                    
            # Ensure proper image format (uint8)
            if screenshot.dtype != np.uint8:
                self.log(f"Converting image from {screenshot.dtype} to uint8")
                screenshot = screenshot.astype(np.uint8)
                
            cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
            self.log(f"已保存语音测试截图: {debug_img_path}")
            
            # 获取截图尺寸
            img_height06181, img_width06181 = screenshot.shape[:2]
            self.log(f"截图尺寸: 宽={img_width06181}, 高={img_height06181}")
            
            # 获取聊天区域尺寸
            chat_width06181 = x2 - x1
            chat_height06181 = y2 - y1
            self.log(f"聊天区域尺寸: 宽={chat_width06181}, 高={chat_height06181}")
            
            # 计算缩放比例 (如果截图尺寸与聊天区域不一致)
            scale_x06181 = chat_width06181 / img_width06181
            scale_y06181 = chat_height06181 / img_height06181
            self.log(f"缩放比例: x={scale_x06181}, y={scale_y06181}")
            
            # 检测语音红点
            self.log("正在检测语音消息红点...")
            voice_red_dots06181 = find_red_dot_with_nms(debug_img_path)
            
            if voice_red_dots06181:
                self.log(f"找到 {len(voice_red_dots06181)} 个语音消息红点")
                
                # 获取第一个红点的坐标
                dot_x06181, dot_y06181 = voice_red_dots06181[0]
                self.log(f"处理语音红点，截图中的相对坐标: ({dot_x06181}, {dot_y06181})")
                
                # 应用缩放比例
                scaled_x06181 = int(dot_x06181 * scale_x06181)
                scaled_y06181 = int(dot_y06181 * scale_y06181)
                self.log(f"缩放后坐标: ({scaled_x06181}, {scaled_y06181})")
                
                # 计算语音气泡位置 (红点左侧像素处)
                voice_bubble_offset = 100  # 默认值
                if hasattr(self.ui, 'voice_bubble_offset_var'):
                    voice_bubble_offset = self.ui.voice_bubble_offset_var.get()
                voice_bubble_x06181 = scaled_x06181 - int(voice_bubble_offset * scale_x06181)
                # 限制不要超出屏幕边界
                voice_bubble_x06181 = max(10, voice_bubble_x06181)
                
                # 转换为屏幕绝对坐标
                abs_x06181 = x1 + voice_bubble_x06181
                abs_y06181 = y1 + scaled_y06181
                
                # 记录详细的坐标信息用于调试
                self.log(f"聊天区域在屏幕上的位置: ({x1}, {y1}, {x2}, {y2})")
                self.log(f"点击语音消息，屏幕绝对坐标: ({abs_x06181}, {abs_y06181})")
                
                # 确保坐标在有效范围内
                if x1 <= abs_x06181 <= x2 and y1 <= abs_y06181 <= y2:
                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(self.ui, 'mouse_interval_var'):
                        mouse_interval = self.ui.mouse_interval_var.get()
                    
                    # 移动鼠标并点击
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    mouse.position = (abs_x06181, abs_y06181)
                    if platform.system() == "Darwin":
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                    else:
                        mouse.press(Button.right)
                        mouse.release(Button.right)
                        
                    new_abs_x06181 = abs_x06181 + 15
                    new_abs_y06181 = abs_y06181 + 15
                    mouse.position = (new_abs_x06181, new_abs_y06181)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_abs_x06181, new_abs_y06181-50)

                    mouse.position = (new_abs_x06181 + 100, new_abs_y06181 + 100)

                else:
                    self.debug_log(f"警告：计算出的坐标 ({abs_x06181}, {abs_y06181}) 超出了聊天区域范围，调整点击位置")
                    # 调整到区域内的安全位置
                    safe_x = min(max(abs_x06181, x1 + 10), x2 - 10)
                    safe_y = min(max(abs_y06181, y1 + 10), y2 - 10)
                    self.debug_log(f"调整后的安全坐标: ({safe_x}, {safe_y})")

                    # 获取鼠标操作间隔时间
                    mouse_interval = 1.0  # 默认值
                    if hasattr(self.ui, 'mouse_interval_var'):
                        mouse_interval = self.ui.mouse_interval_var.get()
                        
                    time.sleep(mouse_interval)
                    mouse = Controller()
                    mouse.position = (safe_x, safe_y)
                    if platform.system() == "Darwin":
                        mouse.press(Button.left)
                        mouse.press(Button.right)
                        mouse.release(Button.left)
                        mouse.release(Button.right)
                    else:
                        mouse.press(Button.right)
                        mouse.release(Button.right)
                    new_safe_x = safe_x + 15
                    new_safe_y = safe_y + 15
                    mouse.position = (new_safe_x, new_safe_y)
                    time.sleep(mouse_interval)
                    mouse.click(Button.left)
                    time.sleep(mouse_interval)
                    pyautogui.moveTo(new_safe_x, new_safe_y-50)

                # 等待语音消息处理完成
                # 获取等待转文字时间
                voice_wait = 6.0  # 默认值
                if hasattr(self.ui, 'voice_wait_var'):
                    voice_wait = self.ui.voice_wait_var.get()
                self.debug_log(f"等待语音转文字完成，等待时间: {voice_wait}秒")
                time.sleep(voice_wait)
                
                # 再次截图检测红点
                screenshot = capture_screen_area(x1, y1, x2, y2, self.noise_areas)
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                debug_img_path = os.path.join(debug_dir, f"voice_test_after_{timestamp}.png")
                
                # Ensure screenshot is a valid NumPy array
                if screenshot is None:
                    self.log("Error: second screenshot is None")
                    return
                    
                if not isinstance(screenshot, np.ndarray):
                    self.log(f"Converting second screenshot from {type(screenshot)} to numpy array")
                    try:
                        screenshot = np.array(screenshot)
                    except Exception as e:
                        self.log(f"Cannot convert to numpy array: {str(e)}")
                        return
                        
                # Ensure proper image format (uint8)
                if screenshot.dtype != np.uint8:
                    self.log(f"Converting image from {screenshot.dtype} to uint8")
                    screenshot = screenshot.astype(np.uint8)
                    
                cv2.imwrite(debug_img_path, screenshot, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                
                # 再次检测红点
                after_dots06181 = find_red_dot_with_nms(debug_img_path)
                if after_dots06181:
                    self.log(f"仍有 {len(after_dots06181)} 个语音消息红点未处理")
                    
                    # 如果红点数量没有减少，尝试不同策略
                    if len(after_dots06181) >= len(voice_red_dots06181):
                        self.log("红点数量未减少，请先调整参数")
                else:
                    self.log("所有语音消息已处理完成")
            else:
                self.log("未检测到语音消息红点")
            
            # 更新预览
            self.update_preview()
        except Exception as e:
            self.log(f"测试语音功能出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
    
    def show_conversation_history(self):
        """显示对话历史记录"""
        # 创建对话历史界面
        history_ui = HistoryUI(self.root, self.conversations, 
                               save_callback=self.storage_manager.save_conversation)
        history_ui.show_window()
    
    def load_conversations(self):
        """加载所有对话历史"""
        conversations, message_counts = self.storage_manager.load_all_conversations()
        self.conversations = conversations
        self.contact_message_counts = message_counts
        self.log(f"已加载{len(self.conversations)}个联系人的聊天历史")
    
    def save_api_settings(self, api_settings):
        """保存API设置"""
        # 将新的设置合并到当前设置中
        self.settings.update(api_settings)
        
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("API设置已保存")
        
        # 输出调试信息
        self.debug_log(f"保存的API设置: provider={self.settings.get('provider')}")
        if self.settings.get('provider') == 'openai':
            self.debug_log(f"OpenAI API设置: URL={self.settings.get('api_base_url')}, Model={self.settings.get('model_name')}")
        else:  # coze
            self.debug_log(f"Coze Space API设置: Bot ID={self.settings.get('bot_id')}")
    
    def save_prompt_templates(self, templates_data):
        """保存提示词模板设置"""
        # 更新设置对象中的提示词模板和默认系统提示词
        self.settings['prompt_templates'] = templates_data.get('prompt_templates', [])
        self.settings['default_system_prompt'] = templates_data.get('default_system_prompt', self.settings.get('system_prompt', ''))
        
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("提示词模板设置已保存")
    
    def save_daily_summary_settings(self, daily_summary_settings):
        """保存每日总结设置"""
        # 更新设置字典中的每日总结相关设置
        self.settings.update(daily_summary_settings)
        # 保存到文件
        self.storage_manager.save_settings(self.settings)
        self.log("每日总结设置已保存")
        
        # 重新调度每日总结任务
        self.schedule_daily_summary()
    
    def schedule_daily_summary(self):
        """安排每日总结定时任务"""
        from datetime import datetime, timedelta
        import time
        
        # 取消之前的调度
        if hasattr(self, 'scheduled_summary_id') and self.scheduled_summary_id:
            self.root.after_cancel(self.scheduled_summary_id)
            self.scheduled_summary_id = None
        
        # 获取每日总结时间
        daily_summary_time = self.settings.get('daily_summary_time', '18:00')
        
        try:
            # 解析时间
            hour, minute = map(int, daily_summary_time.split(':'))
            
            # 当前时间
            now = datetime.now()
            
            # 今天的总结时间点
            target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果今天的时间已经过了，就设置为明天
            if now > target_time:
                target_time += timedelta(days=1)
            
            # 计算到目标时间的毫秒数
            delay_ms = int((target_time - now).total_seconds() * 1000)
            
            # 设置定时器
            self.scheduled_summary_id = self.root.after(delay_ms, self.execute_scheduled_summary)
            
            # 记录日志
            next_run = target_time.strftime('%Y-%m-%d %H:%M:%S')
            self.log(f"每日总结已调度，将于 {next_run} 执行")
            
        except Exception as e:
            self.log(f"调度每日总结任务失败: {str(e)}")
    
    def execute_scheduled_summary(self):
        """执行定时的每日总结任务并重新调度下一次执行"""
        self.log("执行定时的每日总结任务...")
        
        # 执行总结
        self.run_summary_now()
        
        # 重新调度下一次执行
        self.schedule_daily_summary()
    
    def run_summary_now(self):
        """立即执行每日总结"""
        self.log("正在执行每日总结...")
        
        try:
            from datetime import datetime, timedelta
            import json
            import os
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            from email.header import Header
            from openai import OpenAI
            import tkinter as tk
            from tkinter import ttk
            
            # 创建一个进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("每日总结进度")
            progress_window.geometry("500x300")
            progress_window.resizable(False, False)
            
            # 窗口居中
            window_width = 500
            window_height = 300
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # 添加标题
            title_label = ttk.Label(progress_window, text="正在生成每日总结...", font=('微软雅黑', 14, 'bold'))
            title_label.pack(pady=10)
            
            # 添加进度条
            progress = ttk.Progressbar(progress_window, orient="horizontal", length=400, mode="indeterminate")
            progress.pack(pady=10)
            progress.start()
            
            # 添加当前处理状态标签
            status_label = ttk.Label(progress_window, text="准备中...", font=('微软雅黑', 10))
            status_label.pack(pady=5)
            
            # 添加日志文本框
            log_frame = ttk.Frame(progress_window)
            log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
            
            log_label = ttk.Label(log_frame, text="处理日志:", font=('微软雅黑', 10, 'bold'))
            log_label.pack(anchor=tk.W)
            
            log_text = tk.Text(log_frame, wrap=tk.WORD, height=8, width=50)
            log_text.pack(fill=tk.BOTH, expand=True)
            log_text.config(state=tk.DISABLED)  # 设置为只读
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(log_text)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            log_text.config(yscrollcommand=scrollbar.set)
            scrollbar.config(command=log_text.yview)
            
            # 更新日志的函数
            def update_log(message):
                log_text.config(state=tk.NORMAL)
                log_text.insert(tk.END, f"{message}\n")
                log_text.see(tk.END)  # 滚动到底部
                log_text.config(state=tk.DISABLED)
                progress_window.update()  # 更新窗口
            
            # 更新状态的函数
            def update_status(message):
                status_label.config(text=message)
                progress_window.update()
            
            update_log("开始执行每日总结...")
            
            # 创建一个线程来执行耗时操作
            def summary_worker():
                try:
                    # 获取24小时前的时间戳
                    now = datetime.now()
                    yesterday = now - timedelta(days=1)
                    yesterday_timestamp = yesterday.timestamp()
                    
                    # 汇总结果
                    summary_results = []
                    has_data = False
                    
                    # 遍历聊天历史目录中的所有文件
                    chat_dir = self.storage_manager.storage_dir
                    update_log(f"扫描聊天记录目录: {chat_dir}")
                    
                    # 获取所有JSON文件
                    json_files = [f for f in os.listdir(chat_dir) if f.endswith('.json')]
                    total_files = len(json_files)
                    processed_files = 0
                    
                    for filename in json_files:
                        processed_files += 1
                        contact_name = os.path.splitext(filename)[0]
                        file_path = os.path.join(chat_dir, filename)
                        
                        current_progress = int(processed_files / total_files * 100)
                        update_status(f"正在处理: {contact_name} ({processed_files}/{total_files})")
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                conversation_data = json.load(f)
                            
                            # 过滤24小时内的消息
                            recent_messages = []
                            has_recent = False
                            
                            for msg in conversation_data:
                                # 检查消息是否有时间戳字段，且时间在24小时内
                                if 'timestamp' in msg and float(msg['timestamp']) >= yesterday_timestamp:
                                    recent_messages.append(msg)
                                    has_recent = True
                            
                            # 如果有最近的消息，进行总结
                            if has_recent:
                                has_data = True
                                msg_count = len(recent_messages)
                                update_log(f"发现{msg_count}条与{contact_name}的最近聊天记录")
                                
                                # 格式化聊天内容，便于AI总结
                                chat_content = ""
                                for msg in recent_messages:
                                    if msg["role"] == "user":
                                        chat_content += f"{contact_name}：{msg.get('content', '')}\n"
                                    elif msg["role"] == "assistant":
                                        chat_content += f"AI助手：{msg.get('content', '')}\n"
                                
                                # 调用AI进行总结
                                update_status(f"正在总结{contact_name}的聊天记录...")
                                update_log(f"分析{contact_name}的聊天内容中...")
                                summary = self.summarize_chat(contact_name, chat_content)
                                
                                # 添加到汇总结果
                                summary_results.append(f"## {contact_name} 聊天总结\n\n{summary}\n\n")
                                update_log(f"✓ {contact_name}的聊天总结已完成")
                            else:
                                update_log(f"跳过{contact_name}: 24小时内无聊天记录")
                        except Exception as e:
                            update_log(f"处理{contact_name}记录时出错: {str(e)}")
                            self.log(f"处理 {contact_name} 的聊天记录时出错: {str(e)}")
                    
                    # 如果没有数据，记录信息并返回
                    if not has_data:
                        update_status("完成: 无需生成总结")
                        update_log("过去24小时内没有聊天记录，无需生成总结")
                        self.log("过去24小时内没有聊天记录，无需生成总结")
                        
                        # 设置一个定时器，5秒后关闭进度窗口
                        progress_window.after(5000, progress_window.destroy)
                        return
                    
                    # 生成日报内容
                    update_status("正在生成日报...")
                    report_title = f"# 微信聊天日报 - {now.strftime('%Y-%m-%d')}\n\n"
                    report_content = report_title + "\n".join(summary_results)
                    
                    # 保存日报文件
                    report_dir = "daily_reports"
                    os.makedirs(report_dir, exist_ok=True)
                    report_file = os.path.join(report_dir, f"daily_report_{now.strftime('%Y%m%d')}.md")
                    
                    with open(report_file, 'w', encoding='utf-8') as f:
                        f.write(report_content)
                        
                    update_log(f"日报已生成: {report_file}")
                    self.log(f"日报已生成: {report_file}")
                    
                    # 发送邮件
                    update_status("正在发送邮件报告...")
                    update_log("正在发送邮件...")
                    self.send_summary_email(report_content, now.strftime('%Y-%m-%d'))
                    
                    update_status("每日总结已完成")
                    update_log("每日总结已全部完成，邮件已发送!")
                    self.log("每日总结已完成，日报已发送")
                    
                    # 停止进度条动画
                    progress.stop()
                    progress.config(mode="determinate", value=100)
                    
                    # 添加关闭按钮
                    close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                    close_btn.pack(pady=10)
                    
                except Exception as e:
                    self.log(f"执行每日总结时出错: {str(e)}")
                    import traceback
                    self.log(f"错误详情: {traceback.format_exc()}")
                    
                    # 如果进度窗口存在，显示错误
                    if 'progress_window' in locals() and progress_window.winfo_exists():
                        update_status("执行过程中出错")
                        update_log(f"错误: {str(e)}")
                        
                        # 停止进度条动画
                        progress.stop()
                        
                        # 添加关闭按钮
                        close_btn = ttk.Button(progress_window, text="关闭", command=progress_window.destroy)
                        close_btn.pack(pady=10)
            
            # 启动工作线程
            summary_thread = threading.Thread(target=summary_worker, daemon=True)
            summary_thread.start()
            
        except Exception as e:
            self.log(f"启动每日总结线程时出错: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
    
    def summarize_chat(self, contact_name, chat_content):
        """使用AI总结聊天内容"""
        try:
            # Import OpenAI client
            from openai import OpenAI
            
            # 获取API密钥和设置
            provider = self.settings.get('provider', 'openai')
            
            if provider == 'openai':
                api_key = self.settings.get('api_key')
                api_base_url = self.settings.get('api_base_url')
                model_name = self.settings.get('model_name')
            else:
                # 对于其他提供商，如Coze，这里应该返回错误
                return "总结失败: 目前仅支持OpenAI API进行总结"
                
            if not api_key:
                return "无法生成总结：未配置API密钥"
                
            # 从设置中获取总结提示词，如果没有则使用默认提示词
            summary_prompt = self.settings.get('daily_summary_prompt', '')
            if not summary_prompt:
                # 默认提示词
                summary_prompt = f"""
你是一个聊天总结专家，有这丰富的识别问题和提炼要点的能力。

请对以下聊天对话内容进行总结：
{{chat_content}}

总结输出格式：
1.用户主要的疑问和顾虑
2.用户的性格和情绪
3.用户的意向
4.整体总结
"""
            
            # 替换提示词中的{chat_content}占位符
            system_prompt = summary_prompt.replace("{chat_content}", chat_content)

            # 调用OpenAI API
            client = OpenAI(api_key=api_key, base_url=api_base_url)
            
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": "请你总结"}
                ],
                stream=False
            )
            print(system_prompt)
            return response.choices[0].message.content
            
        except Exception as e:
            self.log(f"AI总结时出错: {str(e)}")
            return f"总结失败: {str(e)}"
        
    def send_summary_email(self, report_content, date_str):
        return main2_send_summary_email(self, report_content, date_str)

    def get_system_prompt_for_contact(self, contact_name):
        return main2_get_system_prompt_for_contact(self, contact_name)

    def save_conversation(self, contact_name, conversation_data):
        return main2_save_conversation(self, contact_name, conversation_data)

    def switch_contact(self, new_contact_name):
        return main2_switch_contact(self, new_contact_name)

    def process_chat_area(self):
        return main2_process_chat_area(self)

    def get_filtered_conversation_history(self, contact_name):
        return main2_get_filtered_conversation_history(self, contact_name)

    def detect_and_reply_to_messages(self, messages):
        return main2_detect_and_reply_to_messages(self, messages)
    
    def detect_new_messages(self, current_messages):
        return main2_detect_new_messages(self, current_messages)

    def get_settings(self):
        """获取当前应用设置"""
        return {
            'provider': self.settings.get('provider', 'openai'),
            'api_key': self.settings.get('api_key', ''),
            'api_base_url': self.settings.get('api_base_url', 'https://api.openai.com'),
            'model_name': self.settings.get('model_name', 'gpt-3.5-turbo'),
            'debug_mode': self.debug_mode,
            'chat_area': self.chat_area,
            'input_area': self.input_area,
            'contact_area': self.contact_area,
            'contact_name_area': self.contact_name_area,
            'unique_id_area': self.settings.get('unique_id_area'),  # 添加唯一标识区
            'unread_bubble_area': self.unread_bubble_area,
            'search_area': self.search_area,
            'noise_areas': self.noise_areas,
            'bot_id': self.settings.get('bot_id', ''),
            'token': self.settings.get('token', ''),
            'daily_summary_time': self.settings.get('daily_summary_time', '18:00'),
            'daily_summary_prompt': self.settings.get('daily_summary_prompt', ''),
            'mail_host': self.settings.get('mail_host', ''),
            'mail_user': self.settings.get('mail_user', ''),
            'mail_pass': self.settings.get('mail_pass', ''),
            'receivers': self.settings.get('receivers', '')
        }

    def save_chat_summary_settings(self, chat_summary_settings):
        return main2_save_chat_summary_settings(self, chat_summary_settings)

    def save_settings(self, settings):
        return main2_save_settings(self, settings)

    def generate_chat_summary(self, contact_name):
        return main2_generate_chat_summary(self, contact_name)

    def reply_to_message(self, message_text):
        return main2_reply_to_message(self, message_text)



    

def main():
    """主函数"""
    root = tk.Tk()
    app = ScreenChatBot(root)
    app.setup_ui()
    root.mainloop()

if __name__ == "__main__":
    if not check_lisence():
        sys.exit(0)
    main() 